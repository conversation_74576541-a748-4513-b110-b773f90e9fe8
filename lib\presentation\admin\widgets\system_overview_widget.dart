import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class SystemOverviewWidget extends StatelessWidget {
  final Map<String, dynamic> systemHealth;

  const SystemOverviewWidget({
    Key? key,
    required this.systemHealth,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: _getOverallHealthColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: 'monitor_heart',
                  color: _getOverallHealthColor(),
                  size: 20,
                ),
              ),
              SizedBox(width: 3.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'System Health',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    _getOverallHealthStatus(),
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: _getOverallHealthColor(),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getOverallHealthColor(),
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ],
          ),

          SizedBox(height: 3.h),

          // System components
          Column(
            children: [
              _buildHealthItem(
                'Server Status',
                systemHealth['serverStatus'],
                'dns',
              ),
              SizedBox(height: 2.h),
              _buildHealthItem(
                'Database Status',
                systemHealth['databaseStatus'],
                'storage',
              ),
              SizedBox(height: 2.h),
              _buildMetricItem(
                'API Response Time',
                '${systemHealth['apiResponseTime']}ms',
                'speed',
                systemHealth['apiResponseTime'] < 200 ? 'good' : 'warning',
              ),
              SizedBox(height: 2.h),
              _buildMetricItem(
                'Active Connections',
                systemHealth['activeConnections'].toString(),
                'link',
                'good',
              ),
              SizedBox(height: 2.h),
              _buildMetricItem(
                'Error Rate',
                '${(systemHealth['errorRate'] * 100).toStringAsFixed(2)}%',
                'error_outline',
                systemHealth['errorRate'] < 0.05 ? 'good' : 'error',
              ),
            ],
          ),

          SizedBox(height: 3.h),

          // Performance indicators
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Performance Metrics',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 2.h),
                Row(
                  children: [
                    Expanded(
                      child: _buildPerformanceMetric(
                        'Uptime',
                        '99.9%',
                        Colors.green,
                      ),
                    ),
                    Expanded(
                      child: _buildPerformanceMetric(
                        'CPU Usage',
                        '45%',
                        Colors.blue,
                      ),
                    ),
                    Expanded(
                      child: _buildPerformanceMetric(
                        'Memory',
                        '67%',
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: 2.h),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: Show detailed system logs
                  },
                  icon: CustomIconWidget(
                    iconName: 'list_alt',
                    color: AppTheme.lightTheme.primaryColor,
                    size: 16,
                  ),
                  label: Text(
                    'View Logs',
                    style: TextStyle(
                      color: AppTheme.lightTheme.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: AppTheme.lightTheme.primaryColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Refresh system status
                  },
                  icon: CustomIconWidget(
                    iconName: 'refresh',
                    color: Colors.white,
                    size: 16,
                  ),
                  label: Text(
                    'Refresh',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.lightTheme.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHealthItem(String label, String status, String icon) {
    final isHealthy = status.toLowerCase() == 'healthy';
    final color = isHealthy ? Colors.green : Colors.red;
    
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomIconWidget(
            iconName: icon,
            color: color,
            size: 18,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Text(
            label,
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: 2.w,
            vertical: 0.5.h,
          ),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 6,
                height: 6,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
              SizedBox(width: 1.w),
              Text(
                status.toUpperCase(),
                style: TextStyle(
                  color: color,
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMetricItem(String label, String value, String icon, String status) {
    Color color;
    switch (status) {
      case 'good':
        color = Colors.green;
        break;
      case 'warning':
        color = Colors.orange;
        break;
      case 'error':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }
    
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomIconWidget(
            iconName: icon,
            color: color,
            size: 18,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Text(
            label,
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.w700,
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceMetric(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 16.sp,
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(height: 0.5.h),
        Text(
          label,
          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Color _getOverallHealthColor() {
    final serverHealthy = systemHealth['serverStatus'] == 'healthy';
    final databaseHealthy = systemHealth['databaseStatus'] == 'healthy';
    final apiGood = systemHealth['apiResponseTime'] < 200;
    final errorRateGood = systemHealth['errorRate'] < 0.05;
    
    if (serverHealthy && databaseHealthy && apiGood && errorRateGood) {
      return Colors.green;
    } else if (serverHealthy && databaseHealthy) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  String _getOverallHealthStatus() {
    final color = _getOverallHealthColor();
    if (color == Colors.green) {
      return 'All systems operational';
    } else if (color == Colors.orange) {
      return 'Some issues detected';
    } else {
      return 'Critical issues found';
    }
  }
}
