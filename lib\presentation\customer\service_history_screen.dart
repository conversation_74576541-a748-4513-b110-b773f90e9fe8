import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../core/app_export.dart';
import '../../core/services/order_service.dart';

class ServiceHistoryScreen extends StatefulWidget {
  const ServiceHistoryScreen({Key? key}) : super(key: key);

  @override
  State<ServiceHistoryScreen> createState() => _ServiceHistoryScreenState();
}

class _ServiceHistoryScreenState extends State<ServiceHistoryScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  bool _isLoading = true;
  List<OrderModel> _orders = [];
  String _selectedFilter = 'All';
  final List<String> _filterOptions = ['All', 'Completed', 'Cancelled', 'In Progress', 'Pending'];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadServiceHistory();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  Future<void> _loadServiceHistory() async {
    try {
      final orderService = OrderService.instance;
      final orders = await orderService.getOrders();
      
      setState(() {
        _orders = orders;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _orders = [];
        _isLoading = false;
      });
    }
  }

  List<OrderModel> get _filteredOrders {
    switch (_selectedFilter) {
      case 'Completed':
        return _orders.where((order) => order.status == AppConstants.orderStatusCompleted).toList();
      case 'Cancelled':
        return _orders.where((order) => order.status == AppConstants.orderStatusCancelled).toList();
      case 'In Progress':
        return _orders.where((order) => 
          order.status == AppConstants.orderStatusInProgress || 
          order.status == AppConstants.orderStatusConfirmed ||
          order.status == AppConstants.orderStatusAccepted
        ).toList();
      case 'Pending':
        return _orders.where((order) => order.status == AppConstants.orderStatusPending).toList();
      default:
        return _orders;
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('ประวัติการใช้บริการ'),
        backgroundColor: AppTheme.lightTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadServiceHistory,
            icon: const Icon(Icons.refresh),
            tooltip: 'รีเฟรช',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Filter Section
            _buildFilterSection(),
            
            // Content
            Expanded(
              child: _isLoading
                  ? _buildLoadingState()
                  : _filteredOrders.isEmpty
                      ? _buildEmptyState()
                      : _buildOrdersList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'กรองตามสถานะ',
            style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2.h),
          SizedBox(
            height: 5.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: _filterOptions.length,
              separatorBuilder: (context, index) => SizedBox(width: 2.w),
              itemBuilder: (context, index) {
                final option = _filterOptions[index];
                final isSelected = _selectedFilter == option;
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedFilter = option;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? AppTheme.lightTheme.primaryColor 
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(
                        color: isSelected 
                            ? AppTheme.lightTheme.primaryColor 
                            : Colors.grey[300]!,
                      ),
                    ),
                    child: Text(
                      _getFilterDisplayName(option),
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[700],
                        fontWeight: FontWeight.w600,
                        fontSize: 12.sp,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              AppTheme.lightTheme.primaryColor,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            'กำลังโหลดประวัติ...',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 80,
            color: Colors.grey[400],
          ),
          SizedBox(height: 2.h),
          Text(
            _selectedFilter == 'All' ? 'ยังไม่มีประวัติการใช้บริการ' : 'ไม่พบข้อมูลตามที่กรอง',
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            _selectedFilter == 'All' 
                ? 'เมื่อคุณใช้บริการแล้ว ประวัติจะแสดงที่นี่'
                : 'ลองเปลี่ยนตัวกรองหรือรีเฟรชข้อมูล',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 3.h),
          ElevatedButton(
            onPressed: _loadServiceHistory,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.lightTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('รีเฟรชข้อมูล'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    return RefreshIndicator(
      onRefresh: _loadServiceHistory,
      child: ListView.separated(
        padding: EdgeInsets.all(4.w),
        itemCount: _filteredOrders.length,
        separatorBuilder: (context, index) => SizedBox(height: 2.h),
        itemBuilder: (context, index) {
          final order = _filteredOrders[index];
          return _buildOrderCard(order);
        },
      ),
    );
  }

  Widget _buildOrderCard(OrderModel order) {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(
          context,
          AppRoutes.orderDetailScreen,
          arguments: {'orderId': order.id},
        );
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  order.id,
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
                ),
                _buildStatusChip(order.status),
              ],
            ),

            SizedBox(height: 2.h),

            // Service Type & Date
            Row(
              children: [
                Icon(
                  _getServiceIcon(order.serviceType),
                  size: 20,
                  color: AppTheme.lightTheme.primaryColor,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    _getServiceTypeName(order.serviceType),
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  DateFormat('dd/MM/yyyy').format(order.createdAt),
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),

            SizedBox(height: 1.h),

            // Location
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 1.w),
                Expanded(
                  child: Text(
                    order.pickupLocation.address,
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            if (order.dropoffLocation != null) ...[
              SizedBox(height: 0.5.h),
              Row(
                children: [
                  Icon(
                    Icons.flag,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  SizedBox(width: 1.w),
                  Expanded(
                    child: Text(
                      order.dropoffLocation!.address,
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],

            SizedBox(height: 2.h),

            // Price & Vehicle
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (order.finalPrice != null || order.estimatedPrice != null)
                  Text(
                    '฿${(order.finalPrice ?? order.estimatedPrice!).toStringAsFixed(0)}',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppTheme.lightTheme.primaryColor,
                    ),
                  ),
                Text(
                  '${order.vehicleInfo.make} ${order.vehicleInfo.model}',
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color statusColor = _getStatusColor(status);
    String statusText = _getStatusText(status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: statusColor,
          fontWeight: FontWeight.w600,
          fontSize: 10.sp,
        ),
      ),
    );
  }

  String _getFilterDisplayName(String filter) {
    switch (filter) {
      case 'All':
        return 'ทั้งหมด';
      case 'Completed':
        return 'เสร็จสิ้น';
      case 'Cancelled':
        return 'ยกเลิก';
      case 'In Progress':
        return 'กำลังดำเนินการ';
      case 'Pending':
        return 'รอการยืนยัน';
      default:
        return filter;
    }
  }

  String _getServiceTypeName(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return 'รถลาก';
      case AppConstants.serviceTypeJumpStart:
        return 'จั๊มแบตเตอรี่';
      case AppConstants.serviceTypeFlatTire:
        return 'เปลี่ยนยาง';
      case AppConstants.serviceTypeFuelDelivery:
        return 'ส่งน้ำมัน';
      case AppConstants.serviceTypeLockout:
        return 'ไขกุญแจ';
      default:
        return serviceType;
    }
  }

  IconData _getServiceIcon(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return Icons.local_shipping;
      case AppConstants.serviceTypeJumpStart:
        return Icons.battery_charging_full;
      case AppConstants.serviceTypeFlatTire:
        return Icons.tire_repair;
      case AppConstants.serviceTypeFuelDelivery:
        return Icons.local_gas_station;
      case AppConstants.serviceTypeLockout:
        return Icons.lock_open;
      default:
        return Icons.build;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return Colors.orange;
      case AppConstants.orderStatusConfirmed:
        return Colors.blue;
      case AppConstants.orderStatusInProgress:
        return Colors.purple;
      case AppConstants.orderStatusCompleted:
        return Colors.green;
      case AppConstants.orderStatusCancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return 'รอการยืนยัน';
      case AppConstants.orderStatusConfirmed:
        return 'ยืนยันแล้ว';
      case AppConstants.orderStatusInProgress:
        return 'กำลังดำเนินการ';
      case AppConstants.orderStatusCompleted:
        return 'เสร็จสิ้น';
      case AppConstants.orderStatusCancelled:
        return 'ยกเลิกแล้ว';
      default:
        return status;
    }
  }
}
