import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../core/app_export.dart';
import '../../core/services/order_service.dart';

class ServiceHistoryScreen extends StatefulWidget {
  const ServiceHistoryScreen({Key? key}) : super(key: key);

  @override
  State<ServiceHistoryScreen> createState() => _ServiceHistoryScreenState();
}

class _ServiceHistoryScreenState extends State<ServiceHistoryScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  bool _isLoading = true;
  List<OrderModel> _orders = [];
  String _selectedFilter = 'All';
  final List<String> _filterOptions = ['All', 'Completed', 'Cancelled', 'In Progress', 'Pending'];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadServiceHistory();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  Future<void> _loadServiceHistory() async {
    try {
      final orderService = OrderService.instance;
      final orders = await orderService.getOrders();
      
      setState(() {
        _orders = orders;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _orders = _getMockOrders(); // Use mock data as fallback
        _isLoading = false;
      });
    }
  }

  List<OrderModel> get _filteredOrders {
    switch (_selectedFilter) {
      case 'Completed':
        return _orders.where((order) => order.status == AppConstants.orderStatusCompleted).toList();
      case 'Cancelled':
        return _orders.where((order) => order.status == AppConstants.orderStatusCancelled).toList();
      case 'In Progress':
        return _orders.where((order) => 
          order.status == AppConstants.orderStatusInProgress || 
          order.status == AppConstants.orderStatusConfirmed ||
          order.status == AppConstants.orderStatusAccepted
        ).toList();
      case 'Pending':
        return _orders.where((order) => order.status == AppConstants.orderStatusPending).toList();
      default:
        return _orders;
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('ประวัติการใช้บริการ'),
        backgroundColor: AppTheme.lightTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadServiceHistory,
            icon: const Icon(Icons.refresh),
            tooltip: 'รีเฟรช',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Filter Section
            _buildFilterSection(),
            
            // Content
            Expanded(
              child: _isLoading
                  ? _buildLoadingState()
                  : _filteredOrders.isEmpty
                      ? _buildEmptyState()
                      : _buildOrdersList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'กรองตามสถานะ',
            style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2.h),
          SizedBox(
            height: 5.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: _filterOptions.length,
              separatorBuilder: (context, index) => SizedBox(width: 2.w),
              itemBuilder: (context, index) {
                final option = _filterOptions[index];
                final isSelected = _selectedFilter == option;
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedFilter = option;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? AppTheme.lightTheme.primaryColor 
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(
                        color: isSelected 
                            ? AppTheme.lightTheme.primaryColor 
                            : Colors.grey[300]!,
                      ),
                    ),
                    child: Text(
                      _getFilterDisplayName(option),
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[700],
                        fontWeight: FontWeight.w600,
                        fontSize: 12.sp,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              AppTheme.lightTheme.primaryColor,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            'กำลังโหลดประวัติ...',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 80,
            color: Colors.grey[400],
          ),
          SizedBox(height: 2.h),
          Text(
            _selectedFilter == 'All' ? 'ยังไม่มีประวัติการใช้บริการ' : 'ไม่พบข้อมูลตามที่กรอง',
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            _selectedFilter == 'All' 
                ? 'เมื่อคุณใช้บริการแล้ว ประวัติจะแสดงที่นี่'
                : 'ลองเปลี่ยนตัวกรองหรือรีเฟรชข้อมูล',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 3.h),
          ElevatedButton(
            onPressed: _loadServiceHistory,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.lightTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('รีเฟรชข้อมูล'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    return RefreshIndicator(
      onRefresh: _loadServiceHistory,
      child: ListView.separated(
        padding: EdgeInsets.all(4.w),
        itemCount: _filteredOrders.length,
        separatorBuilder: (context, index) => SizedBox(height: 2.h),
        itemBuilder: (context, index) {
          final order = _filteredOrders[index];
          return _buildOrderCard(order);
        },
      ),
    );
  }

  Widget _buildOrderCard(OrderModel order) {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(
          context,
          AppRoutes.orderDetailScreen,
          arguments: {'orderId': order.id},
        );
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  order.id,
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
                ),
                _buildStatusChip(order.status),
              ],
            ),

            SizedBox(height: 2.h),

            // Service Type & Date
            Row(
              children: [
                Icon(
                  _getServiceIcon(order.serviceType),
                  size: 20,
                  color: AppTheme.lightTheme.primaryColor,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    _getServiceTypeName(order.serviceType),
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  DateFormat('dd/MM/yyyy').format(order.createdAt),
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),

            SizedBox(height: 1.h),

            // Location
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 1.w),
                Expanded(
                  child: Text(
                    'จุดรับ: ${order.pickupLocation.latitude.toStringAsFixed(6)}, ${order.pickupLocation.longitude.toStringAsFixed(6)}',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            if (order.dropoffLocation != null) ...[
              SizedBox(height: 0.5.h),
              Row(
                children: [
                  Icon(
                    Icons.flag,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  SizedBox(width: 1.w),
                  Expanded(
                    child: Text(
                      'จุดส่ง: ${order.dropoffLocation!.latitude.toStringAsFixed(6)}, ${order.dropoffLocation!.longitude.toStringAsFixed(6)}',
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],

            SizedBox(height: 2.h),

            // Price & Vehicle
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (order.finalPrice != null || order.estimatedPrice != null)
                  Text(
                    '฿${(order.finalPrice ?? order.estimatedPrice!).toStringAsFixed(0)}',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppTheme.lightTheme.primaryColor,
                    ),
                  ),
                Text(
                  _getVehicleDisplayText(order.vehicleInfo),
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color statusColor = _getStatusColor(status);
    String statusText = _getStatusText(status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: statusColor,
          fontWeight: FontWeight.w600,
          fontSize: 10.sp,
        ),
      ),
    );
  }

  String _getFilterDisplayName(String filter) {
    switch (filter) {
      case 'All':
        return 'ทั้งหมด';
      case 'Completed':
        return 'เสร็จสิ้น';
      case 'Cancelled':
        return 'ยกเลิก';
      case 'In Progress':
        return 'กำลังดำเนินการ';
      case 'Pending':
        return 'รอการยืนยัน';
      default:
        return filter;
    }
  }

  String _getServiceTypeName(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return 'รถลาก';
      case AppConstants.serviceTypeJumpStart:
        return 'จั๊มแบตเตอรี่';
      case AppConstants.serviceTypeFlatTire:
        return 'เปลี่ยนยาง';
      case AppConstants.serviceTypeFuelDelivery:
        return 'ส่งน้ำมัน';
      case AppConstants.serviceTypeLockout:
        return 'ไขกุญแจ';
      default:
        return serviceType;
    }
  }

  IconData _getServiceIcon(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return Icons.local_shipping;
      case AppConstants.serviceTypeJumpStart:
        return Icons.battery_charging_full;
      case AppConstants.serviceTypeFlatTire:
        return Icons.tire_repair;
      case AppConstants.serviceTypeFuelDelivery:
        return Icons.local_gas_station;
      case AppConstants.serviceTypeLockout:
        return Icons.lock_open;
      default:
        return Icons.build;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return Colors.orange;
      case AppConstants.orderStatusConfirmed:
        return Colors.blue;
      case AppConstants.orderStatusInProgress:
        return Colors.purple;
      case AppConstants.orderStatusCompleted:
        return Colors.green;
      case AppConstants.orderStatusCancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return 'รอการยืนยัน';
      case AppConstants.orderStatusConfirmed:
        return 'ยืนยันแล้ว';
      case AppConstants.orderStatusInProgress:
        return 'กำลังดำเนินการ';
      case AppConstants.orderStatusCompleted:
        return 'เสร็จสิ้น';
      case AppConstants.orderStatusCancelled:
        return 'ยกเลิกแล้ว';
      default:
        return status;
    }
  }

  String _getVehicleDisplayText(VehicleInfo vehicleInfo) {
    List<String> parts = [];

    // Add make if not default
    if (vehicleInfo.make.isNotEmpty && vehicleInfo.make != 'ไม่ระบุ') {
      parts.add(vehicleInfo.make);
    }

    // Add model if not default and not empty
    if (vehicleInfo.model.isNotEmpty && vehicleInfo.model != 'ไม่ระบุ') {
      parts.add(vehicleInfo.model);
    }

    // Add color if not default
    if (vehicleInfo.color.isNotEmpty && vehicleInfo.color != 'ไม่ระบุ') {
      parts.add('สี${vehicleInfo.color}');
    }

    // Add license plate if not default
    if (vehicleInfo.licensePlate.isNotEmpty && vehicleInfo.licensePlate != 'ไม่ระบุ') {
      parts.add('ทะเบียน ${vehicleInfo.licensePlate}');
    }

    return parts.isNotEmpty ? parts.join(' ') : 'ข้อมูลรถยนต์';
  }

  List<OrderModel> _getMockOrders() {
    return [
      // 1. Pending Order
      OrderModel(
        id: 'MOCK001',
        customerId: 'CUST001',
        serviceType: AppConstants.serviceTypeTowing,
        status: AppConstants.orderStatusPending,
        description: 'รถเสียบนทางด่วน ต้องการรถลาก',
        pickupLocation: LocationInfo(
          latitude: 13.7563,
          longitude: 100.5018,
          address: 'หน้าร้าน 7-Eleven ถนนสุขุมวิท',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        dropoffLocation: LocationInfo(
          latitude: 13.7308,
          longitude: 100.5418,
          address: 'อู่ซ่อมรถ ถนนพระราม 4',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        createdAt: DateTime.now().subtract(const Duration(minutes: 15)),
        estimatedPrice: 1500.0,
        vehicleInfo: VehicleInfo(
          make: 'Toyota',
          model: 'Camry',
          year: 'ไม่ระบุ',
          color: 'ขาว',
          licensePlate: 'กข-1234',
        ),
      ),

      // 2. Confirmed Order
      OrderModel(
        id: 'MOCK002',
        customerId: 'CUST001',
        serviceType: AppConstants.serviceTypeJumpStart,
        status: AppConstants.orderStatusConfirmed,
        description: 'แบตเตอรี่หมด ต้องการจั๊มสตาร์ท',
        pickupLocation: LocationInfo(
          latitude: 13.7460,
          longitude: 100.5392,
          address: 'ลานจอดรถ Central World',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        estimatedPrice: 800.0,
        vehicleInfo: VehicleInfo(
          make: 'Honda',
          model: 'Civic',
          year: 'ไม่ระบุ',
          color: 'ดำ',
          licensePlate: 'นม-5678',
        ),
      ),

      // 3. In Progress Order
      OrderModel(
        id: 'MOCK003',
        customerId: 'CUST001',
        serviceType: AppConstants.serviceTypeFlatTire,
        status: AppConstants.orderStatusInProgress,
        description: 'ยางแตก ต้องการเปลี่ยนยางใหม่',
        pickupLocation: LocationInfo(
          latitude: 13.7997,
          longitude: 100.5533,
          address: 'ตลาดจตุจักร ประตู 1',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        dropoffLocation: LocationInfo(
          latitude: 13.8200,
          longitude: 100.5500,
          address: 'ร้านยาง ถนนพหลโยธิน',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        estimatedPrice: 600.0,
        vehicleInfo: VehicleInfo(
          make: 'Mazda',
          model: '3',
          year: 'ไม่ระบุ',
          color: 'แดง',
          licensePlate: 'บจ-9012',
        ),
      ),

      // 4. Completed Order
      OrderModel(
        id: 'MOCK004',
        customerId: 'CUST001',
        serviceType: AppConstants.serviceTypeFuelDelivery,
        status: AppConstants.orderStatusCompleted,
        description: 'น้ำมันหมด ต้องการส่งน้ำมัน',
        pickupLocation: LocationInfo(
          latitude: 13.7200,
          longitude: 100.5100,
          address: 'ถนนสีลม หน้าธนาคาร',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        completedAt: DateTime.now().subtract(const Duration(days: 1, hours: -1)),
        estimatedPrice: 500.0,
        finalPrice: 500.0,
        vehicleInfo: VehicleInfo(
          make: 'BMW',
          model: 'X5',
          year: 'ไม่ระบุ',
          color: 'เงิน',
          licensePlate: 'กก-1111',
        ),
      ),

      // 5. Cancelled Order
      OrderModel(
        id: 'MOCK005',
        customerId: 'CUST001',
        serviceType: AppConstants.serviceTypeLockout,
        status: AppConstants.orderStatusCancelled,
        description: 'กุญแจล็อคในรถ ต้องการไขกุญแจ',
        pickupLocation: LocationInfo(
          latitude: 13.7800,
          longitude: 100.5600,
          address: 'ห้างสรรพสินค้า MBK Center',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        estimatedPrice: 700.0,
        vehicleInfo: VehicleInfo(
          make: 'Mercedes-Benz',
          model: 'C200',
          year: 'ไม่ระบุ',
          color: 'น้ำเงิน',
          licensePlate: 'นน-2222',
        ),
      ),
    ];
  }
}
