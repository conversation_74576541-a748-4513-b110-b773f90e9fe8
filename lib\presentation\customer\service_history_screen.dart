import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../core/app_export.dart';

class ServiceHistoryScreen extends StatefulWidget {
  const ServiceHistoryScreen({Key? key}) : super(key: key);

  @override
  State<ServiceHistoryScreen> createState() => _ServiceHistoryScreenState();
}

class _ServiceHistoryScreenState extends State<ServiceHistoryScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  bool _isLoading = true;
  List<OrderModel> _orders = [];
  String _selectedFilter = 'All';
  final List<String> _filterOptions = ['All', 'Completed', 'Cancelled', 'In Progress'];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadServiceHistory();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  Future<void> _loadServiceHistory() async {
    // Simulate loading service history
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _orders = [
        OrderModel(
          id: 'order1',
          customerId: 'customer1',
          supplierId: 'supplier1',
          serviceType: AppConstants.serviceTypeTowing,
          status: AppConstants.orderStatusCompleted,
          description: 'Car breakdown on highway',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Sukhumvit Road, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Toyota',
            model: 'Camry',
            year: '2020',
            color: 'White',
            licensePlate: 'ABC-1234',
          ),
          createdAt: DateTime.now().subtract(const Duration(days: 7)),
          completedAt: DateTime.now().subtract(const Duration(days: 7, hours: -2)),
          finalPrice: 1500.0,
          rating: OrderRating(
            id: 'rating1',
            orderId: 'order1',
            customerId: 'customer1',
            supplierId: 'supplier1',
            rating: 5,
            comment: 'Excellent service!',
            createdAt: DateTime.now().subtract(const Duration(days: 7)),
          ),
        ),
        OrderModel(
          id: 'order2',
          customerId: 'customer1',
          supplierId: 'supplier2',
          serviceType: AppConstants.serviceTypeJumpStart,
          status: AppConstants.orderStatusCompleted,
          description: 'Dead battery in parking lot',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Central World, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Honda',
            model: 'Civic',
            year: '2019',
            color: 'Black',
            licensePlate: 'XYZ-5678',
          ),
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          completedAt: DateTime.now().subtract(const Duration(days: 15, hours: -1)),
          finalPrice: 800.0,
          rating: OrderRating(
            id: 'rating2',
            orderId: 'order2',
            customerId: 'customer1',
            supplierId: 'supplier2',
            rating: 4,
            comment: 'Good service',
            createdAt: DateTime.now().subtract(const Duration(days: 15)),
          ),
        ),
        OrderModel(
          id: 'order3',
          customerId: 'customer1',
          serviceType: AppConstants.serviceTypeFlatTire,
          status: AppConstants.orderStatusCancelled,
          description: 'Flat tire on front left',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Silom Road, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Nissan',
            model: 'Almera',
            year: '2021',
            color: 'Silver',
            licensePlate: 'DEF-9012',
          ),
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          estimatedPrice: 600.0,
        ),
        OrderModel(
          id: 'order4',
          customerId: 'customer1',
          supplierId: 'supplier3',
          serviceType: AppConstants.serviceTypeFuelDelivery,
          status: AppConstants.orderStatusCompleted,
          description: 'Ran out of fuel',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Chatuchak Market, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Toyota',
            model: 'Vios',
            year: '2018',
            color: 'Red',
            licensePlate: 'GHI-3456',
          ),
          createdAt: DateTime.now().subtract(const Duration(days: 45)),
          completedAt: DateTime.now().subtract(const Duration(days: 45, hours: -1)),
          finalPrice: 450.0,
          rating: OrderRating(
            id: 'rating4',
            orderId: 'order4',
            customerId: 'customer1',
            supplierId: 'supplier3',
            rating: 5,
            comment: 'Perfect service!',
            createdAt: DateTime.now().subtract(const Duration(days: 45)),
          ),
        ),
      ];
      _isLoading = false;
    });
  }

  List<OrderModel> get _filteredOrders {
    switch (_selectedFilter) {
      case 'Completed':
        return _orders.where((order) => order.isCompleted).toList();
      case 'Cancelled':
        return _orders.where((order) => order.isCancelled).toList();
      case 'In Progress':
        return _orders.where((order) => order.isInProgress || order.isAccepted).toList();
      default:
        return _orders;
    }
  }

  void _handleOrderTap(OrderModel order) {
    Navigator.pushNamed(
      context,
      '/order-details-screen',
      arguments: {'orderId': order.id},
    );
  }

  void _handleReorder(OrderModel order) {
    Navigator.pushNamed(
      context,
      AppRoutes.requestServiceScreen,
      arguments: {
        'serviceType': order.serviceType,
        'vehicleInfo': order.vehicleInfo,
        'pickupLocation': order.pickupLocation,
      },
    );
  }

  void _handleRateOrder(OrderModel order) {
    Navigator.pushNamed(
      context,
      AppRoutes.ratingScreen,
      arguments: {'orderId': order.id},
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Service History'),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back_ios',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // Filter tabs
                  Container(
                    padding: EdgeInsets.all(4.w),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: _filterOptions.map((filter) {
                          final isSelected = _selectedFilter == filter;
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedFilter = filter;
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.only(right: 2.w),
                              padding: EdgeInsets.symmetric(
                                horizontal: 4.w,
                                vertical: 1.h,
                              ),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? AppTheme.lightTheme.primaryColor
                                    : AppTheme.lightTheme.cardColor,
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: isSelected
                                      ? AppTheme.lightTheme.primaryColor
                                      : AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
                                ),
                              ),
                              child: Text(
                                filter,
                                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                  color: isSelected
                                      ? Colors.white
                                      : AppTheme.lightTheme.colorScheme.onSurface,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),

                  // Orders list
                  Expanded(
                    child: _filteredOrders.isEmpty
                        ? _buildEmptyState()
                        : RefreshIndicator(
                            onRefresh: _loadServiceHistory,
                            child: ListView.separated(
                              padding: EdgeInsets.all(4.w),
                              itemCount: _filteredOrders.length,
                              separatorBuilder: (context, index) => SizedBox(height: 2.h),
                              itemBuilder: (context, index) {
                                final order = _filteredOrders[index];
                                return _buildOrderCard(order);
                              },
                            ),
                          ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'history',
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              size: 64,
            ),
            SizedBox(height: 2.h),
            Text(
              'No service history',
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              _selectedFilter == 'All'
                  ? 'You haven\'t used our services yet. Request your first service to get started!'
                  : 'No ${_selectedFilter.toLowerCase()} orders found.',
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            if (_selectedFilter == 'All') ...[
              SizedBox(height: 3.h),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.pushNamed(context, AppRoutes.requestServiceScreen);
                },
                icon: CustomIconWidget(
                  iconName: 'add',
                  color: Colors.white,
                  size: 20,
                ),
                label: const Text(
                  'Request Service',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOrderCard(OrderModel order) {
    return GestureDetector(
      onTap: () => _handleOrderTap(order),
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: _getStatusColor(order.status).withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: _getServiceColor(order.serviceType).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CustomIconWidget(
                        iconName: _getServiceIcon(order.serviceType),
                        color: _getServiceColor(order.serviceType),
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order.serviceType,
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'Order #${order.id.length > 8 ? order.id.substring(0, 8) : order.id}',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                _buildStatusChip(order.status),
              ],
            ),

            SizedBox(height: 2.h),

            // Vehicle and location info
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'directions_car',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Text(
                  '${order.vehicleInfo.make} ${order.vehicleInfo.model} (${order.vehicleInfo.licensePlate})',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            SizedBox(height: 1.h),

            Row(
              children: [
                CustomIconWidget(
                  iconName: 'location_on',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    order.pickupLocation.address,
                    style: AppTheme.lightTheme.textTheme.bodyMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            SizedBox(height: 2.h),

            // Date and price
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  DateFormat('MMM dd, yyyy • HH:mm').format(order.createdAt),
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  ),
                ),
                if (order.finalPrice != null || order.estimatedPrice != null)
                  Text(
                    '${AppConstants.currencySymbol}${(order.finalPrice ?? order.estimatedPrice)?.toStringAsFixed(0)}',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      color: AppTheme.lightTheme.primaryColor,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
              ],
            ),

            // Rating display for completed orders
            if (order.isCompleted && order.rating != null) ...[
              SizedBox(height: 1.h),
              Row(
                children: [
                  Text(
                    'Your rating: ',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  ...List.generate(5, (index) {
                    return CustomIconWidget(
                      iconName: index < order.rating!.rating ? 'star' : 'star_border',
                      color: index < order.rating!.rating ? Colors.amber : Colors.grey,
                      size: 16,
                    );
                  }),
                ],
              ),
            ],

            // Action buttons
            if (order.isCompleted) ...[
              SizedBox(height: 2.h),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _handleReorder(order),
                      icon: CustomIconWidget(
                        iconName: 'refresh',
                        color: AppTheme.lightTheme.primaryColor,
                        size: 16,
                      ),
                      label: Text(
                        'Reorder',
                        style: TextStyle(
                          color: AppTheme.lightTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: AppTheme.lightTheme.primaryColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  if (order.rating == null) ...[
                    SizedBox(width: 2.w),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _handleRateOrder(order),
                        icon: CustomIconWidget(
                          iconName: 'star',
                          color: Colors.white,
                          size: 16,
                        ),
                        label: Text(
                          'Rate',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.lightTheme.primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 3.w,
        vertical: 1.h,
      ),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        status.toUpperCase(),
        style: TextStyle(
          color: _getStatusColor(status),
          fontSize: 10.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return Colors.orange;
      case AppConstants.orderStatusAccepted:
        return Colors.blue;
      case AppConstants.orderStatusInProgress:
        return Colors.purple;
      case AppConstants.orderStatusCompleted:
        return Colors.green;
      case AppConstants.orderStatusCancelled:
        return Colors.red;
      case AppConstants.orderStatusRejected:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  Color _getServiceColor(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return Colors.red;
      case AppConstants.serviceTypeJumpStart:
        return Colors.orange;
      case AppConstants.serviceTypeFlatTire:
        return Colors.blue;
      case AppConstants.serviceTypeFuelDelivery:
        return Colors.green;
      case AppConstants.serviceTypeLockout:
        return Colors.purple;
      case AppConstants.serviceTypeWinchOut:
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }

  String _getServiceIcon(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return 'local_shipping';
      case AppConstants.serviceTypeJumpStart:
        return 'battery_charging_full';
      case AppConstants.serviceTypeFlatTire:
        return 'tire_repair';
      case AppConstants.serviceTypeFuelDelivery:
        return 'local_gas_station';
      case AppConstants.serviceTypeLockout:
        return 'lock_open';
      case AppConstants.serviceTypeWinchOut:
        return 'construction';
      default:
        return 'build';
    }
  }
}
