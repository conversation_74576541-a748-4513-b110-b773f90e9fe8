import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class AgentStatusCardWidget extends StatelessWidget {
  final AgentModel agent;

  const AgentStatusCardWidget({
    Key? key,
    required this.agent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomIconWidget(
                iconName: 'verified_user',
                color: Colors.white,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'Agent Status',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 2.h),
          
          Row(
            children: [
              // Status indicator
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 3.w,
                  vertical: 1.h,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor(agent.status).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _getStatusColor(agent.status).withValues(alpha: 0.5),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _getStatusColor(agent.status),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      _getStatusText(agent.status),
                      style: TextStyle(
                        color: _getStatusColor(agent.status),
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              
              const Spacer(),
              
              // Agent ID
              Text(
                'ID: ${(agent.id.length > 8 ? agent.id.substring(0, 8) : agent.id).toUpperCase()}',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 11.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 2.h),
          
          // Stats row
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  label: 'Total Orders',
                  value: agent.totalOrders.toString(),
                  icon: 'assignment',
                ),
              ),
              Container(
                width: 1,
                height: 5.h,
                color: Colors.white.withValues(alpha: 0.3),
              ),
              Expanded(
                child: _buildStatItem(
                  label: 'Success Rate',
                  value: '${agent.completionRate.toStringAsFixed(1)}%',
                  icon: 'trending_up',
                ),
              ),
              Container(
                width: 1,
                height: 5.h,
                color: Colors.white.withValues(alpha: 0.3),
              ),
              Expanded(
                child: _buildStatItem(
                  label: 'Commission',
                  value: '${(agent.commissionRate * 100).toStringAsFixed(0)}%',
                  icon: 'percent',
                ),
              ),
            ],
          ),
          
          if (agent.approvedAt != null) ...[
            SizedBox(height: 2.h),
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'schedule',
                  color: Colors.white.withValues(alpha: 0.7),
                  size: 14,
                ),
                SizedBox(width: 1.w),
                Text(
                  'Agent since ${_formatDate(agent.approvedAt!)}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required String label,
    required String value,
    required String icon,
  }) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomIconWidget(
            iconName: icon,
            color: Colors.white,
            size: 18,
          ),
        ),
        SizedBox(height: 1.h),
        Text(
          value,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.w700,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 9.sp,
            fontWeight: FontWeight.w400,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.agentStatusApproved:
        return Colors.green;
      case AppConstants.agentStatusPending:
        return Colors.orange;
      case AppConstants.agentStatusRejected:
        return Colors.red;
      case AppConstants.agentStatusSuspended:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case AppConstants.agentStatusApproved:
        return 'Active Agent';
      case AppConstants.agentStatusPending:
        return 'Pending Approval';
      case AppConstants.agentStatusRejected:
        return 'Application Rejected';
      case AppConstants.agentStatusSuspended:
        return 'Account Suspended';
      default:
        return 'Unknown Status';
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }
}
