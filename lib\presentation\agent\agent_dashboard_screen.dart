import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import 'widgets/agent_status_card_widget.dart';
import 'widgets/commission_card_widget.dart';
import 'widgets/customer_leads_widget.dart';
import 'widgets/agent_orders_widget.dart';

class AgentDashboardScreen extends StatefulWidget {
  const AgentDashboardScreen({Key? key}) : super(key: key);

  @override
  State<AgentDashboardScreen> createState() => _AgentDashboardScreenState();
}

class _AgentDashboardScreenState extends State<AgentDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  bool _isLoading = true;
  AgentModel? _agentData;
  List<OrderModel> _agentOrders = [];
  List<Map<String, dynamic>> _customerLeads = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadAgentData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  Future<void> _loadAgentData() async {
    // Simulate loading agent data
    await Future.delayed(const Duration(seconds: 1));

    // Mock agent data
    setState(() {
      _agentData = AgentModel(
        id: 'agent1',
        userId: 'user1',
        applicationId: 'app1',
        status: AppConstants.agentStatusApproved,
        companyName: 'Bangkok Agent Services',
        latitude: 13.7563,
        longitude: 100.5018,
        address: '123 Sukhumvit Road',
        city: 'Bangkok',
        state: 'Bangkok',
        zipCode: '10110',
        phoneNumber: '02-123-4567',
        commissionRate: 0.10,
        totalEarnings: 15600.0,
        totalOrders: 78,
        completedOrders: 72,
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
        approvedAt: DateTime.now().subtract(const Duration(days: 170)),
      );

      // Mock agent orders
      _agentOrders = [
        OrderModel(
          id: 'order1',
          customerId: 'customer1',
          agentId: 'agent1',
          serviceType: AppConstants.serviceTypeTowing,
          status: AppConstants.orderStatusInProgress,
          description: 'Car breakdown assistance',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Sukhumvit Road, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Toyota',
            model: 'Camry',
            year: '2020',
            color: 'White',
            licensePlate: 'ABC-1234',
          ),
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          estimatedPrice: 1500.0,
        ),
        OrderModel(
          id: 'order2',
          customerId: 'customer2',
          agentId: 'agent1',
          serviceType: AppConstants.serviceTypeJumpStart,
          status: AppConstants.orderStatusCompleted,
          description: 'Dead battery service',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Central World, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Honda',
            model: 'Civic',
            year: '2019',
            color: 'Black',
            licensePlate: 'XYZ-5678',
          ),
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          completedAt: DateTime.now().subtract(const Duration(hours: 22)),
          finalPrice: 800.0,
        ),
      ];

      // Mock customer leads
      _customerLeads = [
        {
          'id': 'lead1',
          'name': 'John Doe',
          'phone': '************',
          'location': 'Sukhumvit Road, Bangkok',
          'serviceNeeded': AppConstants.serviceTypeTowing,
          'urgency': 'High',
          'estimatedValue': 1500.0,
          'distance': 2.5,
          'createdAt': DateTime.now().subtract(const Duration(minutes: 15)),
        },
        {
          'id': 'lead2',
          'name': 'Jane Smith',
          'phone': '************',
          'location': 'Silom Road, Bangkok',
          'serviceNeeded': AppConstants.serviceTypeFlatTire,
          'urgency': 'Medium',
          'estimatedValue': 600.0,
          'distance': 4.2,
          'createdAt': DateTime.now().subtract(const Duration(hours: 1)),
        },
      ];

      _isLoading = false;
    });
  }

  void _handleFindCustomers() {
    Navigator.pushNamed(context, AppRoutes.findCustomersScreen);
  }

  void _handleAgentOrders() {
    Navigator.pushNamed(context, AppRoutes.agentOrdersScreen);
  }

  void _handleProfile() {
    Navigator.pushNamed(context, AppRoutes.profileScreen);
  }

  void _handleNotifications() {
    Navigator.pushNamed(context, AppRoutes.notificationsScreen);
  }



  void _handleContactCustomer(Map<String, dynamic> lead) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Contact ${lead['name']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Phone: ${lead['phone']}'),
            Text('Service: ${lead['serviceNeeded']}'),
            Text('Location: ${lead['location']}'),
            Text('Estimated Value: ${AppConstants.currencySymbol}${lead['estimatedValue'].toStringAsFixed(0)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement call functionality
            },
            icon: CustomIconWidget(
              iconName: 'phone',
              color: Colors.white,
              size: 16,
            ),
            label: const Text('Call Customer'),
          ),
        ],
      ),
    );
  }

  void _handleCreateOrder(Map<String, dynamic> lead) {
    Navigator.pushNamed(
      context,
      AppRoutes.requestServiceScreen,
      arguments: {
        'agentMode': true,
        'customerInfo': lead,
        'serviceType': lead['serviceNeeded'],
      },
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: _loadAgentData,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Container(
                          padding: EdgeInsets.all(4.w),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme.lightTheme.primaryColor,
                                AppTheme.lightTheme.primaryColor.withValues(alpha: 0.8),
                              ],
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Top bar
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Agent Dashboard',
                                        style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                      Text(
                                        _agentData?.companyName ?? 'Agent',
                                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                          color: Colors.white.withValues(alpha: 0.9),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      IconButton(
                                        onPressed: _handleNotifications,
                                        icon: CustomIconWidget(
                                          iconName: 'notifications',
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                      ),
                                      IconButton(
                                        onPressed: _handleProfile,
                                        icon: CircleAvatar(
                                          radius: 16,
                                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                                          child: CustomIconWidget(
                                            iconName: 'person',
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),

                              SizedBox(height: 3.h),

                              // Agent status
                              if (_agentData != null)
                                AgentStatusCardWidget(agent: _agentData!),
                            ],
                          ),
                        ),

                        SizedBox(height: 3.h),

                        // Commission card
                        if (_agentData != null)
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: CommissionCardWidget(agent: _agentData!),
                          ),

                        SizedBox(height: 3.h),

                        // Quick actions
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Quick Actions',
                                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(height: 2.h),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildQuickActionCard(
                                      title: 'Find Customers',
                                      subtitle: 'Search for leads',
                                      icon: 'search',
                                      color: Colors.blue,
                                      onTap: _handleFindCustomers,
                                    ),
                                  ),
                                  SizedBox(width: 3.w),
                                  Expanded(
                                    child: _buildQuickActionCard(
                                      title: 'My Orders',
                                      subtitle: 'Track progress',
                                      icon: 'assignment',
                                      color: Colors.green,
                                      onTap: _handleAgentOrders,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        SizedBox(height: 3.h),

                        // Customer leads
                        if (_customerLeads.isNotEmpty) ...[
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Customer Leads',
                                      style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 2.w,
                                        vertical: 0.5.h,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.orange,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        '${_customerLeads.length}',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 10.sp,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 2.h),
                                CustomerLeadsWidget(
                                  leads: _customerLeads,
                                  onContactCustomer: _handleContactCustomer,
                                  onCreateOrder: _handleCreateOrder,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 3.h),
                        ],

                        // Recent orders
                        if (_agentOrders.isNotEmpty) ...[
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Recent Orders',
                                      style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                    TextButton(
                                      onPressed: _handleAgentOrders,
                                      child: Text(
                                        'View All',
                                        style: TextStyle(
                                          color: AppTheme.lightTheme.primaryColor,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 1.h),
                                AgentOrdersWidget(
                                  orders: _agentOrders.take(3).toList(),
                                  isPreview: true,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 3.h),
                        ],

                        // Empty state
                        if (_customerLeads.isEmpty && _agentOrders.isEmpty) ...[
                          Padding(
                            padding: EdgeInsets.all(8.w),
                            child: Column(
                              children: [
                                CustomIconWidget(
                                  iconName: 'people_outline',
                                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                  size: 64,
                                ),
                                SizedBox(height: 2.h),
                                Text(
                                  'No customer leads yet',
                                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                SizedBox(height: 1.h),
                                Text(
                                  'Start finding customers to grow your business and earn commissions.',
                                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                SizedBox(height: 3.h),
                                ElevatedButton.icon(
                                  onPressed: _handleFindCustomers,
                                  icon: CustomIconWidget(
                                    iconName: 'search',
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  label: const Text(
                                    'Find Customers',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildQuickActionCard({
    required String title,
    required String subtitle,
    required String icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 12.h,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color,
              color.withValues(alpha: 0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      subtitle,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 12.w,
                height: 12.w,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: CustomIconWidget(
                    iconName: icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
