import 'package:flutter/material.dart';
import '../presentation/splash_screen/splash_screen.dart';
import '../presentation/login_screen/login_screen.dart';
import '../presentation/auth/register_screen_new.dart';
import '../presentation/auth/forgot_password_screen.dart';
import '../presentation/auth/otp_verification_screen.dart';
import '../presentation/customer/customer_dashboard_screen.dart';
import '../presentation/customer/request_service_screen.dart';
import '../presentation/customer/track_service_screen.dart';
import '../presentation/customer/service_history_screen.dart';
import '../presentation/customer/rating_screen.dart';
import '../presentation/supplier/supplier_dashboard_screen.dart';
import '../presentation/agent/agent_dashboard_screen.dart';
import '../presentation/admin/admin_dashboard_screen.dart';
import '../presentation/common/profile_screen.dart';
import '../presentation/common/notifications_screen.dart';

class AppRoutes {
  // Initial routes
  static const String initial = '/';
  static const String splashScreen = '/splash-screen';

  // Authentication routes
  static const String loginScreen = '/login-screen';
  static const String registerScreen = '/register-screen';
  static const String forgotPasswordScreen = '/forgot-password-screen';
  static const String otpVerificationScreen = '/otp-verification-screen';

  // Dashboard routes
  static const String customerDashboard = '/customer-dashboard';
  static const String supplierDashboard = '/supplier-dashboard';
  static const String agentDashboard = '/agent-dashboard';
  static const String adminDashboard = '/admin-dashboard';

  // Customer routes
  static const String requestServiceScreen = '/request-service-screen';
  static const String trackServiceScreen = '/track-service-screen';
  static const String serviceHistoryScreen = '/service-history-screen';
  static const String ratingScreen = '/rating-screen';
  static const String profileScreen = '/profile-screen';
  static const String notificationsScreen = '/notifications-screen';


  // Supplier routes
  static const String jobManagementScreen = '/job-management-screen';
  static const String vehicleManagementScreen = '/vehicle-management-screen';
  static const String earningsScreen = '/earnings-screen';

  // Agent routes
  static const String agentApplicationScreen = '/agent-application-screen';
  static const String findCustomersScreen = '/find-customers-screen';
  static const String agentOrdersScreen = '/agent-orders-screen';

  // Admin routes
  static const String userManagementScreen = '/user-management-screen';
  static const String agentApprovalScreen = '/agent-approval-screen';
  static const String analyticsScreen = '/analytics-screen';
  static const String systemSettingsScreen = '/system-settings-screen';

  // Common routes
  static const String settingsScreen = '/settings-screen';
  static const String helpScreen = '/help-screen';

  static Map<String, WidgetBuilder> routes = {
    // Initial routes
    initial: (context) => const SplashScreen(),
    splashScreen: (context) => const SplashScreen(),

    // Authentication routes
    loginScreen: (context) => const LoginScreen(),
    registerScreen: (context) => const RegisterScreen(),
    forgotPasswordScreen: (context) => const ForgotPasswordScreen(),
    otpVerificationScreen: (context) => const OtpVerificationScreen(),

    // Dashboard routes
    customerDashboard: (context) => const CustomerDashboardScreen(),
    supplierDashboard: (context) => const SupplierDashboardScreen(),
    agentDashboard: (context) => const AgentDashboardScreen(),
    adminDashboard: (context) => const AdminDashboardScreen(),

    // Customer routes
    requestServiceScreen: (context) => const RequestServiceScreen(),
    trackServiceScreen: (context) => const TrackServiceScreen(),
    serviceHistoryScreen: (context) => const ServiceHistoryScreen(),
    ratingScreen: (context) => const RatingScreen(),

    // Common routes
    profileScreen: (context) => const ProfileScreen(),
    notificationsScreen: (context) => const NotificationsScreen(),
  };
}
