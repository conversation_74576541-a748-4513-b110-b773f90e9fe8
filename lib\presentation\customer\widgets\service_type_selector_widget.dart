import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ServiceTypeSelectorWidget extends StatelessWidget {
  final String? selectedServiceType;
  final Function(String) onServiceTypeChanged;

  const ServiceTypeSelectorWidget({
    Key? key,
    this.selectedServiceType,
    required this.onServiceTypeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final services = [
      {
        'type': AppConstants.serviceTypeTowing,
        'title': 'Towing Service',
        'description': 'Vehicle breakdown, accident, or mechanical failure',
        'icon': 'local_shipping',
        'color': Colors.red,
        'estimatedTime': '15-30 min',
        'basePrice': 1200,
      },
      {
        'type': AppConstants.serviceTypeJumpStart,
        'title': 'Jump Start',
        'description': 'Dead battery assistance and charging',
        'icon': 'battery_charging_full',
        'color': Colors.orange,
        'estimatedTime': '10-20 min',
        'basePrice': 600,
      },
      {
        'type': AppConstants.serviceTypeFlatTire,
        'title': 'Flat Tire Change',
        'description': 'Tire replacement or repair service',
        'icon': 'tire_repair',
        'color': Colors.blue,
        'estimatedTime': '15-25 min',
        'basePrice': 500,
      },
      {
        'type': AppConstants.serviceTypeFuelDelivery,
        'title': 'Fuel Delivery',
        'description': 'Emergency fuel delivery service',
        'icon': 'local_gas_station',
        'color': Colors.green,
        'estimatedTime': '20-30 min',
        'basePrice': 400,
      },
      {
        'type': AppConstants.serviceTypeLockout,
        'title': 'Vehicle Lockout',
        'description': 'Locked out of your vehicle assistance',
        'icon': 'lock_open',
        'color': Colors.purple,
        'estimatedTime': '10-20 min',
        'basePrice': 800,
      },
      {
        'type': AppConstants.serviceTypeWinchOut,
        'title': 'Winch Out Service',
        'description': 'Vehicle stuck in mud, snow, or ditch',
        'icon': 'construction',
        'color': Colors.brown,
        'estimatedTime': '20-40 min',
        'basePrice': 1000,
      },
    ];

    return ListView.separated(
      itemCount: services.length,
      separatorBuilder: (context, index) => SizedBox(height: 2.h),
      itemBuilder: (context, index) {
        final service = services[index];
        final isSelected = selectedServiceType == service['type'];
        
        return GestureDetector(
          onTap: () => onServiceTypeChanged(service['type'] as String),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: isSelected 
                  ? AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1)
                  : AppTheme.lightTheme.cardColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected 
                    ? AppTheme.lightTheme.primaryColor
                    : AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: isSelected
                      ? AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1)
                      : Colors.black.withValues(alpha: 0.05),
                  blurRadius: isSelected ? 12 : 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Icon container
                Container(
                  width: 15.w,
                  height: 15.w,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        service['color'] as Color,
                        (service['color'] as Color).withValues(alpha: 0.7),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: CustomIconWidget(
                      iconName: service['icon'] as String,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                ),
                
                SizedBox(width: 4.w),
                
                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            service['title'] as String,
                            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: isSelected 
                                  ? AppTheme.lightTheme.primaryColor
                                  : AppTheme.lightTheme.colorScheme.onSurface,
                            ),
                          ),
                          if (isSelected)
                            Container(
                              padding: EdgeInsets.all(1.w),
                              decoration: BoxDecoration(
                                color: AppTheme.lightTheme.primaryColor,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: CustomIconWidget(
                                iconName: 'check',
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                        ],
                      ),
                      
                      SizedBox(height: 1.h),
                      
                      Text(
                        service['description'] as String,
                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      SizedBox(height: 1.5.h),
                      
                      Row(
                        children: [
                          // Estimated time
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 2.w,
                              vertical: 0.5.h,
                            ),
                            decoration: BoxDecoration(
                              color: (service['color'] as Color).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CustomIconWidget(
                                  iconName: 'schedule',
                                  color: service['color'] as Color,
                                  size: 14,
                                ),
                                SizedBox(width: 1.w),
                                Text(
                                  service['estimatedTime'] as String,
                                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                                    color: service['color'] as Color,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          SizedBox(width: 3.w),
                          
                          // Base price
                          Text(
                            'From ${AppConstants.currencySymbol}${service['basePrice']}',
                            style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                              color: AppTheme.lightTheme.primaryColor,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
