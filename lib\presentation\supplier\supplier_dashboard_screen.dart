import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import 'widgets/status_toggle_widget.dart';
import 'widgets/earnings_card_widget.dart';
import 'widgets/active_jobs_widget.dart';
import 'widgets/job_requests_widget.dart';

class SupplierDashboardScreen extends StatefulWidget {
  const SupplierDashboardScreen({Key? key}) : super(key: key);

  @override
  State<SupplierDashboardScreen> createState() => _SupplierDashboardScreenState();
}

class _SupplierDashboardScreenState extends State<SupplierDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  String _supplierStatus = AppConstants.supplierStatusOffline;
  bool _isLoading = true;
  SupplierModel? _supplierData;
  List<OrderModel> _activeJobs = [];
  List<OrderModel> _jobRequests = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadSupplierData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  Future<void> _loadSupplierData() async {
    // Simulate loading supplier data
    await Future.delayed(const Duration(seconds: 1));

    // Mock supplier data
    setState(() {
      _supplierData = SupplierModel(
        id: 'supplier1',
        userId: 'user1',
        companyName: 'Bangkok Tow Service',
        businessLicense: 'BTS-2024-001',
        status: AppConstants.supplierStatusOnline,
        latitude: 13.7563,
        longitude: 100.5018,
        address: '123 Sukhumvit Road',
        city: 'Bangkok',
        state: 'Bangkok',
        zipCode: '10110',
        phoneNumber: '02-123-4567',
        serviceTypes: [
          AppConstants.serviceTypeTowing,
          AppConstants.serviceTypeJumpStart,
          AppConstants.serviceTypeFlatTire,
        ],
        rating: 4.8,
        totalJobs: 156,
        completedJobs: 148,
        isVerified: true,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
      );

      _supplierStatus = _supplierData!.status;

      // Mock active jobs
      _activeJobs = [
        OrderModel(
          id: 'order1',
          customerId: 'customer1',
          supplierId: 'supplier1',
          serviceType: AppConstants.serviceTypeTowing,
          status: AppConstants.orderStatusInProgress,
          description: 'Car breakdown on highway',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Sukhumvit Road, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Toyota',
            model: 'Camry',
            year: '2020',
            color: 'White',
            licensePlate: 'ABC-1234',
          ),
          createdAt: DateTime.now().subtract(const Duration(hours: 1)),
          acceptedAt: DateTime.now().subtract(const Duration(minutes: 45)),
          estimatedPrice: 1500.0,
        ),
      ];

      // Mock job requests
      _jobRequests = [
        OrderModel(
          id: 'order2',
          customerId: 'customer2',
          serviceType: AppConstants.serviceTypeJumpStart,
          status: AppConstants.orderStatusPending,
          description: 'Dead battery in parking lot',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Central World, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Honda',
            model: 'Civic',
            year: '2019',
            color: 'Black',
            licensePlate: 'XYZ-5678',
          ),
          createdAt: DateTime.now().subtract(const Duration(minutes: 15)),
          estimatedPrice: 800.0,
        ),
        OrderModel(
          id: 'order3',
          customerId: 'customer3',
          serviceType: AppConstants.serviceTypeFlatTire,
          status: AppConstants.orderStatusPending,
          description: 'Flat tire on front left',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Silom Road, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Nissan',
            model: 'Almera',
            year: '2021',
            color: 'Silver',
            licensePlate: 'DEF-9012',
          ),
          createdAt: DateTime.now().subtract(const Duration(minutes: 8)),
          estimatedPrice: 600.0,
        ),
      ];

      _isLoading = false;
    });
  }

  void _handleStatusToggle(String newStatus) {
    setState(() {
      _supplierStatus = newStatus;
      if (_supplierData != null) {
        _supplierData = _supplierData!.copyWith(status: newStatus);
      }
    });

    // TODO: Update status on server
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Status updated to ${newStatus.toLowerCase()}'),
        backgroundColor: _getStatusColor(newStatus),
      ),
    );
  }

  void _handleJobAction(String orderId, String action) {
    setState(() {
      if (action == 'accept') {
        // Move from requests to active jobs
        final jobIndex = _jobRequests.indexWhere((job) => job.id == orderId);
        if (jobIndex != -1) {
          final job = _jobRequests.removeAt(jobIndex);
          final acceptedJob = job.copyWith(
            status: AppConstants.orderStatusAccepted,
            supplierId: _supplierData?.id,
            acceptedAt: DateTime.now(),
          );
          _activeJobs.add(acceptedJob);
        }
      } else if (action == 'reject') {
        // Remove from requests
        _jobRequests.removeWhere((job) => job.id == orderId);
      }
    });

    // TODO: Send action to server
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Job ${action}ed successfully'),
        backgroundColor: action == 'accept' ? Colors.green : Colors.orange,
      ),
    );
  }

  void _handleNavigateToJob(String orderId) {
    Navigator.pushNamed(
      context,
      '/job-details-screen',
      arguments: {'orderId': orderId},
    );
  }

  void _handleProfile() {
    Navigator.pushNamed(context, AppRoutes.profileScreen);
  }

  void _handleNotifications() {
    Navigator.pushNamed(context, AppRoutes.notificationsScreen);
  }



  void _handleJobManagement() {
    Navigator.pushNamed(context, AppRoutes.jobManagementScreen);
  }

  void _handleVehicleManagement() {
    Navigator.pushNamed(context, AppRoutes.vehicleManagementScreen);
  }

  void _handleEarnings() {
    Navigator.pushNamed(context, AppRoutes.earningsScreen);
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.supplierStatusOnline:
        return Colors.green;
      case AppConstants.supplierStatusBusy:
        return Colors.orange;
      case AppConstants.supplierStatusOffline:
      default:
        return Colors.grey;
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: _loadSupplierData,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Container(
                          padding: EdgeInsets.all(4.w),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme.lightTheme.primaryColor,
                                AppTheme.lightTheme.primaryColor.withValues(alpha: 0.8),
                              ],
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Top bar
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Welcome back!',
                                        style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                      Text(
                                        _supplierData?.companyName ?? 'Supplier',
                                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                          color: Colors.white.withValues(alpha: 0.9),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      IconButton(
                                        onPressed: _handleNotifications,
                                        icon: CustomIconWidget(
                                          iconName: 'notifications',
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                      ),
                                      IconButton(
                                        onPressed: _handleProfile,
                                        icon: CircleAvatar(
                                          radius: 16,
                                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                                          child: CustomIconWidget(
                                            iconName: 'person',
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),

                              SizedBox(height: 3.h),

                              // Status toggle
                              StatusToggleWidget(
                                currentStatus: _supplierStatus,
                                onStatusChanged: _handleStatusToggle,
                              ),
                            ],
                          ),
                        ),

                        SizedBox(height: 3.h),

                        // Earnings card
                        if (_supplierData != null)
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: EarningsCardWidget(
                              supplier: _supplierData!,
                              onTap: _handleEarnings,
                            ),
                          ),

                        SizedBox(height: 3.h),

                        // Quick actions
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Quick Actions',
                                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(height: 2.h),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildQuickActionCard(
                                      title: 'Job Management',
                                      icon: 'work',
                                      color: Colors.blue,
                                      onTap: _handleJobManagement,
                                    ),
                                  ),
                                  SizedBox(width: 3.w),
                                  Expanded(
                                    child: _buildQuickActionCard(
                                      title: 'Vehicles',
                                      icon: 'local_shipping',
                                      color: Colors.green,
                                      onTap: _handleVehicleManagement,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        SizedBox(height: 3.h),

                        // Active jobs
                        if (_activeJobs.isNotEmpty) ...[
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Active Jobs',
                                  style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                SizedBox(height: 2.h),
                                ActiveJobsWidget(
                                  jobs: _activeJobs,
                                  onJobTap: _handleNavigateToJob,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 3.h),
                        ],

                        // Job requests
                        if (_jobRequests.isNotEmpty) ...[
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'New Job Requests',
                                      style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 2.w,
                                        vertical: 0.5.h,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.red,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        '${_jobRequests.length}',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 10.sp,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 2.h),
                                JobRequestsWidget(
                                  requests: _jobRequests,
                                  onJobAction: _handleJobAction,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 3.h),
                        ],

                        // Empty state
                        if (_activeJobs.isEmpty && _jobRequests.isEmpty) ...[
                          Padding(
                            padding: EdgeInsets.all(8.w),
                            child: Column(
                              children: [
                                CustomIconWidget(
                                  iconName: 'work_off',
                                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                  size: 64,
                                ),
                                SizedBox(height: 2.h),
                                Text(
                                  'No jobs available',
                                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                SizedBox(height: 1.h),
                                Text(
                                  _supplierStatus == AppConstants.supplierStatusOffline
                                      ? 'Go online to start receiving job requests'
                                      : 'You\'re all caught up! New jobs will appear here.',
                                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildQuickActionCard({
    required String title,
    required String icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 10.h,
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: icon,
                  color: color,
                  size: 24,
                ),
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              title,
              style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.lightTheme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
