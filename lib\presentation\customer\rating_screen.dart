import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

class RatingScreen extends StatefulWidget {
  const RatingScreen({Key? key}) : super(key: key);

  @override
  State<RatingScreen> createState() => _RatingScreenState();
}

class _RatingScreenState extends State<RatingScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  String? _orderId;
  OrderModel? _order;
  SupplierModel? _supplier;
  int _rating = 0;
  final TextEditingController _reviewController = TextEditingController();
  bool _isLoading = false;
  bool _isSubmitted = false;

  final List<String> _quickReviews = [
    'Excellent service!',
    'Very professional',
    'Quick response time',
    'Fair pricing',
    'Friendly staff',
    'Would recommend',
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _getOrderId();
    _loadOrderData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _fadeController.forward();
  }

  void _getOrderId() {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      _orderId = args['orderId'];
    }
  }

  Future<void> _loadOrderData() async {
    if (_orderId == null) return;

    // Simulate loading order data
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _order = OrderModel(
        id: _orderId!,
        customerId: 'customer1',
        supplierId: 'supplier1',
        serviceType: AppConstants.serviceTypeTowing,
        status: AppConstants.orderStatusCompleted,
        description: 'Car breakdown on highway',
        pickupLocation: LocationInfo(
          latitude: 13.7563,
          longitude: 100.5018,
          address: 'Sukhumvit Road, Bangkok',
        ),
        vehicleInfo: VehicleInfo(
          make: 'Toyota',
          model: 'Camry',
          year: '2020',
          color: 'White',
          licensePlate: 'ABC-1234',
        ),
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        completedAt: DateTime.now().subtract(const Duration(minutes: 30)),
        finalPrice: 1500.0,
      );

      _supplier = SupplierModel(
        id: 'supplier1',
        userId: 'user1',
        companyName: 'Bangkok Tow Service',
        businessLicense: 'BTS-2024-001',
        status: AppConstants.supplierStatusOnline,
        latitude: 13.7563,
        longitude: 100.5018,
        address: '123 Sukhumvit Road',
        city: 'Bangkok',
        state: 'Bangkok',
        zipCode: '10110',
        phoneNumber: '02-123-4567',
        serviceTypes: [AppConstants.serviceTypeTowing],
        rating: 4.8,
        totalJobs: 156,
        completedJobs: 148,
        isVerified: true,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
      );
    });
  }

  void _handleStarTap(int starIndex) {
    setState(() {
      _rating = starIndex + 1;
    });
    
    // Trigger haptic feedback
    HapticFeedback.lightImpact();
    
    // Animate star selection
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });
  }

  void _handleQuickReview(String review) {
    setState(() {
      _reviewController.text = review;
    });
  }

  Future<void> _submitRating() async {
    if (_rating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a rating'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      setState(() {
        _isSubmitted = true;
        _isLoading = false;
      });

      // Trigger success haptic feedback
      HapticFeedback.heavyImpact();

      // Show success animation
      _scaleController.forward();

      // Navigate back after delay
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Thank you for your feedback!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to submit rating. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _getRatingText() {
    switch (_rating) {
      case 1:
        return 'Poor';
      case 2:
        return 'Fair';
      case 3:
        return 'Good';
      case 4:
        return 'Very Good';
      case 5:
        return 'Excellent';
      default:
        return 'Rate your experience';
    }
  }

  Color _getRatingColor() {
    switch (_rating) {
      case 1:
        return Colors.red;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.yellow.shade700;
      case 4:
        return Colors.lightGreen;
      case 5:
        return Colors.green;
      default:
        return AppTheme.lightTheme.colorScheme.onSurfaceVariant;
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    _reviewController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Rate Service'),
        leading: IconButton(
          onPressed: _isSubmitted ? null : () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back_ios',
            color: _isSubmitted 
                ? AppTheme.lightTheme.colorScheme.onSurfaceVariant
                : AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: _isSubmitted
            ? _buildSuccessView()
            : SingleChildScrollView(
                padding: EdgeInsets.all(4.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: 2.h),

                    // Service completed icon
                    Container(
                      width: 20.w,
                      height: 20.w,
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10.w),
                      ),
                      child: Center(
                        child: CustomIconWidget(
                          iconName: 'check_circle',
                          color: Colors.green,
                          size: 40,
                        ),
                      ),
                    ),

                    SizedBox(height: 3.h),

                    // Title
                    Text(
                      'Service Completed!',
                      style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: 1.h),

                    Text(
                      'How was your experience with ${_supplier?.companyName ?? 'the supplier'}?',
                      style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: 4.h),

                    // Order summary
                    if (_order != null)
                      Container(
                        padding: EdgeInsets.all(4.w),
                        decoration: BoxDecoration(
                          color: AppTheme.lightTheme.cardColor,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(2.w),
                                  decoration: BoxDecoration(
                                    color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: CustomIconWidget(
                                    iconName: 'local_shipping',
                                    color: AppTheme.lightTheme.primaryColor,
                                    size: 20,
                                  ),
                                ),
                                SizedBox(width: 3.w),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        _order!.serviceType,
                                        style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Text(
                                        '${_order!.vehicleInfo.make} ${_order!.vehicleInfo.model}',
                                        style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Text(
                                  '${AppConstants.currencySymbol}${_order!.finalPrice?.toStringAsFixed(0)}',
                                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                    color: AppTheme.lightTheme.primaryColor,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                    SizedBox(height: 4.h),

                    // Rating stars
                    Text(
                      _getRatingText(),
                      style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                        color: _getRatingColor(),
                        fontWeight: FontWeight.w700,
                      ),
                    ),

                    SizedBox(height: 2.h),

                    AnimatedBuilder(
                      animation: _scaleAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: List.generate(5, (index) {
                              return GestureDetector(
                                onTap: () => _handleStarTap(index),
                                child: Container(
                                  padding: EdgeInsets.all(2.w),
                                  child: CustomIconWidget(
                                    iconName: index < _rating ? 'star' : 'star_border',
                                    color: index < _rating ? Colors.amber : Colors.grey,
                                    size: 40,
                                  ),
                                ),
                              );
                            }),
                          ),
                        );
                      },
                    ),

                    SizedBox(height: 4.h),

                    // Review text field
                    TextFormField(
                      controller: _reviewController,
                      decoration: InputDecoration(
                        labelText: 'Write a review (optional)',
                        hintText: 'Share your experience...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: AppTheme.lightTheme.primaryColor,
                            width: 2,
                          ),
                        ),
                      ),
                      maxLines: 4,
                      maxLength: 500,
                    ),

                    SizedBox(height: 2.h),

                    // Quick review options
                    Text(
                      'Quick reviews:',
                      style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),

                    SizedBox(height: 1.h),

                    Wrap(
                      spacing: 2.w,
                      runSpacing: 1.h,
                      children: _quickReviews.map((review) {
                        return GestureDetector(
                          onTap: () => _handleQuickReview(review),
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 3.w,
                              vertical: 1.h,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Text(
                              review,
                              style: TextStyle(
                                color: AppTheme.lightTheme.primaryColor,
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),

                    SizedBox(height: 4.h),

                    // Submit button
                    SizedBox(
                      width: double.infinity,
                      height: 6.h,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _submitRating,
                        style: AppTheme.lightTheme.elevatedButtonTheme.style?.copyWith(
                          backgroundColor: WidgetStateProperty.resolveWith((states) {
                            if (states.contains(WidgetState.disabled)) {
                              return AppTheme.lightTheme.colorScheme.onSurfaceVariant
                                  .withValues(alpha: 0.3);
                            }
                            return AppTheme.lightTheme.primaryColor;
                          }),
                        ),
                        child: _isLoading
                            ? SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : Text(
                                'Submit Rating',
                                style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),

                    SizedBox(height: 2.h),

                    // Skip button
                    TextButton(
                      onPressed: _isLoading ? null : () => Navigator.pop(context),
                      child: Text(
                        'Skip for now',
                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildSuccessView() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(8.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 25.w,
                height: 25.w,
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.5.w),
                ),
                child: Center(
                  child: CustomIconWidget(
                    iconName: 'check_circle',
                    color: Colors.green,
                    size: 60,
                  ),
                ),
              ),
              
              SizedBox(height: 3.h),
              
              Text(
                'Thank You!',
                style: AppTheme.lightTheme.textTheme.headlineLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: Colors.green,
                ),
              ),
              
              SizedBox(height: 1.h),
              
              Text(
                'Your rating has been submitted successfully',
                style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              
              SizedBox(height: 2.h),
              
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return CustomIconWidget(
                    iconName: index < _rating ? 'star' : 'star_border',
                    color: index < _rating ? Colors.amber : Colors.grey,
                    size: 24,
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
