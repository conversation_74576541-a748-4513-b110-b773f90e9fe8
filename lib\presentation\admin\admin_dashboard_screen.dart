import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import 'widgets/admin_stats_widget.dart';
import 'widgets/pending_approvals_widget.dart';
import 'widgets/system_overview_widget.dart';
import 'widgets/recent_activities_widget.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({Key? key}) : super(key: key);

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  bool _isLoading = true;
  Map<String, dynamic> _dashboardData = {};

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadDashboardData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  Future<void> _loadDashboardData() async {
    // Simulate loading dashboard data
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _dashboardData = {
        'stats': {
          'totalUsers': 1247,
          'totalOrders': 3456,
          'totalRevenue': 125600.0,
          'activeSuppliers': 89,
          'pendingAgents': 12,
          'todayOrders': 45,
          'monthlyGrowth': 15.6,
          'customerSatisfaction': 4.7,
        },
        'pendingApprovals': [
          {
            'id': 'agent1',
            'name': 'John Smith',
            'type': 'Agent Application',
            'submittedAt': DateTime.now().subtract(const Duration(hours: 2)),
            'location': 'Bangkok',
            'experience': '3 years',
          },
          {
            'id': 'supplier1',
            'name': 'Bangkok Tow Services',
            'type': 'Supplier Registration',
            'submittedAt': DateTime.now().subtract(const Duration(hours: 5)),
            'location': 'Bangkok',
            'vehicles': 5,
          },
        ],
        'recentActivities': [
          {
            'id': '1',
            'type': 'order_completed',
            'description': 'Order #12345 completed successfully',
            'timestamp': DateTime.now().subtract(const Duration(minutes: 15)),
            'user': 'Customer #ABC123',
          },
          {
            'id': '2',
            'type': 'agent_approved',
            'description': 'Agent application approved',
            'timestamp': DateTime.now().subtract(const Duration(hours: 1)),
            'user': 'Agent #DEF456',
          },
          {
            'id': '3',
            'type': 'supplier_registered',
            'description': 'New supplier registered',
            'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
            'user': 'Supplier #GHI789',
          },
        ],
        'systemHealth': {
          'serverStatus': 'healthy',
          'databaseStatus': 'healthy',
          'apiResponseTime': 120,
          'activeConnections': 234,
          'errorRate': 0.02,
        },
      };
      _isLoading = false;
    });
  }

  void _handleUserManagement() {
    Navigator.pushNamed(context, AppRoutes.userManagementScreen);
  }

  void _handleAgentApprovals() {
    Navigator.pushNamed(context, AppRoutes.agentApprovalScreen);
  }

  void _handleAnalytics() {
    Navigator.pushNamed(context, AppRoutes.analyticsScreen);
  }

  void _handleSystemSettings() {
    Navigator.pushNamed(context, AppRoutes.systemSettingsScreen);
  }

  void _handleProfile() {
    Navigator.pushNamed(context, AppRoutes.profileScreen);
  }

  void _handleNotifications() {
    Navigator.pushNamed(context, AppRoutes.notificationsScreen);
  }

  void _handleApprovalAction(String id, String action) {
    setState(() {
      final approvals = _dashboardData['pendingApprovals'] as List;
      approvals.removeWhere((approval) => approval['id'] == id);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Application ${action}ed successfully'),
        backgroundColor: action == 'approve' ? Colors.green : Colors.orange,
      ),
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: _loadDashboardData,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Container(
                          padding: EdgeInsets.all(4.w),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme.lightTheme.primaryColor,
                                AppTheme.lightTheme.primaryColor.withValues(alpha: 0.8),
                              ],
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Top bar
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Admin Dashboard',
                                        style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                      Text(
                                        'TowTruck Pro Management',
                                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                          color: Colors.white.withValues(alpha: 0.9),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      IconButton(
                                        onPressed: _handleNotifications,
                                        icon: Stack(
                                          children: [
                                            CustomIconWidget(
                                              iconName: 'notifications',
                                              color: Colors.white,
                                              size: 24,
                                            ),
                                            if ((_dashboardData['pendingApprovals'] as List?)?.isNotEmpty == true)
                                              Positioned(
                                                right: 0,
                                                top: 0,
                                                child: Container(
                                                  width: 8,
                                                  height: 8,
                                                  decoration: const BoxDecoration(
                                                    color: Colors.red,
                                                    shape: BoxShape.circle,
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                      IconButton(
                                        onPressed: _handleProfile,
                                        icon: CircleAvatar(
                                          radius: 16,
                                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                                          child: CustomIconWidget(
                                            iconName: 'admin_panel_settings',
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),

                              SizedBox(height: 3.h),

                              // Quick stats
                              if (_dashboardData['stats'] != null)
                                Row(
                                  children: [
                                    Expanded(
                                      child: _buildQuickStat(
                                        'Total Users',
                                        _dashboardData['stats']['totalUsers'].toString(),
                                        'people',
                                        Colors.white.withValues(alpha: 0.9),
                                      ),
                                    ),
                                    SizedBox(width: 4.w),
                                    Expanded(
                                      child: _buildQuickStat(
                                        'Today Orders',
                                        _dashboardData['stats']['todayOrders'].toString(),
                                        'assignment',
                                        Colors.white.withValues(alpha: 0.9),
                                      ),
                                    ),
                                    SizedBox(width: 4.w),
                                    Expanded(
                                      child: _buildQuickStat(
                                        'Pending',
                                        _dashboardData['stats']['pendingAgents'].toString(),
                                        'pending',
                                        Colors.orange.shade200,
                                      ),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),

                        SizedBox(height: 3.h),

                        // Admin stats
                        if (_dashboardData['stats'] != null)
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: AdminStatsWidget(stats: _dashboardData['stats']),
                          ),

                        SizedBox(height: 3.h),

                        // Quick actions
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Quick Actions',
                                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(height: 2.h),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildActionCard(
                                      title: 'User Management',
                                      icon: 'people',
                                      color: Colors.blue,
                                      onTap: _handleUserManagement,
                                    ),
                                  ),
                                  SizedBox(width: 3.w),
                                  Expanded(
                                    child: _buildActionCard(
                                      title: 'Agent Approvals',
                                      icon: 'how_to_reg',
                                      color: Colors.orange,
                                      onTap: _handleAgentApprovals,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 2.h),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildActionCard(
                                      title: 'Analytics',
                                      icon: 'analytics',
                                      color: Colors.green,
                                      onTap: _handleAnalytics,
                                    ),
                                  ),
                                  SizedBox(width: 3.w),
                                  Expanded(
                                    child: _buildActionCard(
                                      title: 'System Settings',
                                      icon: 'settings',
                                      color: Colors.purple,
                                      onTap: _handleSystemSettings,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        SizedBox(height: 3.h),

                        // Pending approvals
                        if (_dashboardData['pendingApprovals']?.isNotEmpty == true) ...[
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Pending Approvals',
                                      style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 2.w,
                                        vertical: 0.5.h,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.red,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        '${_dashboardData['pendingApprovals'].length}',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 10.sp,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 2.h),
                                PendingApprovalsWidget(
                                  approvals: _dashboardData['pendingApprovals'],
                                  onApprovalAction: _handleApprovalAction,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 3.h),
                        ],

                        // System overview
                        if (_dashboardData['systemHealth'] != null)
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'System Overview',
                                  style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                SizedBox(height: 2.h),
                                SystemOverviewWidget(
                                  systemHealth: _dashboardData['systemHealth'],
                                ),
                              ],
                            ),
                          ),

                        SizedBox(height: 3.h),

                        // Recent activities
                        if (_dashboardData['recentActivities']?.isNotEmpty == true)
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Recent Activities',
                                  style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                SizedBox(height: 2.h),
                                RecentActivitiesWidget(
                                  activities: _dashboardData['recentActivities'],
                                ),
                              ],
                            ),
                          ),

                        SizedBox(height: 3.h),
                      ],
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, String icon, Color color) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: icon,
            color: color,
            size: 24,
          ),
          SizedBox(height: 1.h),
          Text(
            value,
            style: TextStyle(
              color: Colors.white,
              fontSize: 18.sp,
              fontWeight: FontWeight.w700,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 10.sp,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required String title,
    required String icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 10.h,
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: icon,
                  color: color,
                  size: 24,
                ),
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              title,
              style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.lightTheme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
