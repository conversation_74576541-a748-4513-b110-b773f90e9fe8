import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import 'widgets/recent_orders_widget.dart';
import 'customer_map_screen.dart';

class CustomerDashboardScreen extends StatefulWidget {
  const CustomerDashboardScreen({Key? key}) : super(key: key);

  @override
  State<CustomerDashboardScreen> createState() =>
      _CustomerDashboardScreenState();
}

class _CustomerDashboardScreenState extends State<CustomerDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  bool _isLocationEnabled = false;
  String _currentLocation = 'Getting location...';
  List<OrderModel> _recentOrders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  Future<void> _initializeData() async {
    await _checkLocationPermission();
    await _loadRecentOrders();
    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _checkLocationPermission() async {
    final locationService = LocationService();
    final isInitialized = await locationService.initialize();

    if (isInitialized) {
      final position = await locationService.getCurrentPosition();
      if (position != null) {
        final address = await locationService.getAddressFromCoordinates(
          position.latitude,
          position.longitude,
        );
        setState(() {
          _isLocationEnabled = true;
          _currentLocation = address ?? 'Unknown location';
        });
      }
    } else {
      setState(() {
        _isLocationEnabled = false;
        _currentLocation = 'Location permission required';
      });
    }
  }

  Future<void> _loadRecentOrders() async {
    // Simulate loading recent orders
    await Future.delayed(const Duration(seconds: 1));

    // Mock data
    setState(() {
      _recentOrders = [
        OrderModel(
          id: '1',
          customerId: 'customer1',
          serviceType: AppConstants.serviceTypeTowing,
          status: AppConstants.orderStatusCompleted,
          description: 'Car breakdown on highway',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Sukhumvit Road, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Toyota',
            model: 'Camry',
            year: '2020',
            color: 'White',
            licensePlate: 'ABC-1234',
          ),
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
          finalPrice: 1500.0,
        ),
        OrderModel(
          id: '2',
          customerId: 'customer1',
          serviceType: AppConstants.serviceTypeJumpStart,
          status: AppConstants.orderStatusInProgress,
          description: 'Dead battery',
          pickupLocation: LocationInfo(
            latitude: 13.7563,
            longitude: 100.5018,
            address: 'Silom Road, Bangkok',
          ),
          vehicleInfo: VehicleInfo(
            make: 'Honda',
            model: 'Civic',
            year: '2019',
            color: 'Black',
            licensePlate: 'XYZ-5678',
          ),
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          estimatedPrice: 800.0,
        ),
      ];
    });
  }

  void _handleServiceHistory() {
    Navigator.pushNamed(context, AppRoutes.serviceHistoryScreen);
  }

  void _handleProfile() {
    Navigator.pushNamed(context, AppRoutes.profileScreen);
  }

  void _handleNotifications() {
    Navigator.pushNamed(context, AppRoutes.notificationsScreen);
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: _initializeData,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Container(
                          padding: EdgeInsets.all(4.w),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme.lightTheme.primaryColor,
                                AppTheme.lightTheme.primaryColor
                                    .withValues(alpha: 0.8),
                              ],
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Top bar
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Good ${_getGreeting()}!',
                                        style: AppTheme
                                            .lightTheme.textTheme.headlineSmall
                                            ?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                      Text(
                                        'Need roadside assistance?',
                                        style: AppTheme
                                            .lightTheme.textTheme.bodyMedium
                                            ?.copyWith(
                                          color: Colors.white
                                              .withValues(alpha: 0.9),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      IconButton(
                                        onPressed: _handleNotifications,
                                        icon: CustomIconWidget(
                                          iconName: 'notifications',
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                      ),
                                      IconButton(
                                        onPressed: _handleProfile,
                                        icon: CircleAvatar(
                                          radius: 16,
                                          backgroundColor: Colors.white
                                              .withValues(alpha: 0.2),
                                          child: CustomIconWidget(
                                            iconName: 'person',
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),

                              

                              // Location info
                              !_isLocationEnabled ? 
                              Container(
                                margin: EdgeInsets.only(top: 2.h),
                                padding: EdgeInsets.all(3.w),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  children: [
                                    CustomIconWidget(
                                      iconName: _isLocationEnabled
                                          ? 'location_on'
                                          : 'location_off',
                                      color: _isLocationEnabled
                                          ? Colors.white
                                          : Colors.red[300]!,
                                      size: 20,
                                    ),
                                    SizedBox(width: 2.w),
                                    Expanded(
                                      child: Text(
                                        _currentLocation,
                                        style: AppTheme
                                            .lightTheme.textTheme.bodySmall
                                            ?.copyWith(
                                          color: Colors.white
                                              .withValues(alpha: 0.9),
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    if (!_isLocationEnabled)
                                      TextButton(
                                        onPressed: _checkLocationPermission,
                                        child: Text(
                                          'Enable',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ) : Container()
                            ],
                          ),
                        ),
                        !_isLocationEnabled ? Container() :CustomerMapScreen(),
                        // SizedBox(height: 3.h),

                        // Main Actions
                        // Padding(
                        //   padding: EdgeInsets.symmetric(horizontal: 4.w),
                        //   child: Column(
                        //     children: [
                        //       // Enable Location Button
                        //       Container(
                        //         width: double.infinity,
                        //         height: 8.h,
                        //         child: ElevatedButton(
                        //           onPressed: () {
                        //             Navigator.push(
                        //               context,
                        //               MaterialPageRoute(
                        //                 builder: (context) => const CustomerMapScreen(),
                        //               ),
                        //             );
                        //           },
                        //           style: ElevatedButton.styleFrom(
                        //             backgroundColor: Colors.red,
                        //             foregroundColor: Colors.white,
                        //             elevation: 8,
                        //             shadowColor: Colors.red.withValues(alpha: 0.5),
                        //             shape: RoundedRectangleBorder(
                        //               borderRadius: BorderRadius.circular(16),
                        //             ),
                        //           ),
                        //           child: Row(
                        //             mainAxisAlignment: MainAxisAlignment.center,
                        //             children: [
                        //               Icon(
                        //                 Icons.location_on,
                        //                 size: 32,
                        //               ),
                        //               SizedBox(width: 3.w),
                        //               Text(
                        //                 'Enable Location',
                        //                 style: TextStyle(
                        //                   fontSize: 20.sp,
                        //                   fontWeight: FontWeight.w700,
                        //                 ),
                        //               ),
                        //             ],
                        //           ),
                        //         ),
                        //       ),

                        //       SizedBox(height: 3.h),

                        //       // View History Button
                        //       Container(
                        //         width: double.infinity,
                        //         height: 6.h,
                        //         child: OutlinedButton(
                        //           onPressed: () {
                        //             Navigator.pushNamed(
                        //               context,
                        //               AppRoutes.serviceHistoryScreen,
                        //             );
                        //           },
                        //           style: OutlinedButton.styleFrom(
                        //             foregroundColor: AppTheme.lightTheme.primaryColor,
                        //             side: BorderSide(
                        //               color: AppTheme.lightTheme.primaryColor,
                        //               width: 2,
                        //             ),
                        //             shape: RoundedRectangleBorder(
                        //               borderRadius: BorderRadius.circular(12),
                        //             ),
                        //           ),
                        //           child: Row(
                        //             mainAxisAlignment: MainAxisAlignment.center,
                        //             children: [
                        //               Icon(
                        //                 Icons.history,
                        //                 size: 24,
                        //               ),
                        //               SizedBox(width: 2.w),
                        //               Text(
                        //                 'ดูประวัติ',
                        //                 style: TextStyle(
                        //                   fontSize: 16.sp,
                        //                   fontWeight: FontWeight.w600,
                        //                 ),
                        //               ),
                        //             ],
                        //           ),
                        //         ),
                        //       ),
                        //     ],
                        //   ),
                        // ),

                        SizedBox(height: 3.h),

                        // Recent Orders
                        if (_recentOrders.isNotEmpty) ...[
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Recent Orders',
                                      style: AppTheme
                                          .lightTheme.textTheme.titleLarge
                                          ?.copyWith(
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                    TextButton(
                                      onPressed: _handleServiceHistory,
                                      child: Text(
                                        'View All',
                                        style: TextStyle(
                                          color:
                                              AppTheme.lightTheme.primaryColor,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 1.h),
                                RecentOrdersWidget(orders: _recentOrders),
                              ],
                            ),
                          ),
                          SizedBox(height: 3.h),
                        ],
                      ],
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Morning';
    } else if (hour < 17) {
      return 'Afternoon';
    } else {
      return 'Evening';
    }
  }
}
