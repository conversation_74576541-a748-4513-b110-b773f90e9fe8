import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import 'widgets/recent_orders_widget.dart';
import 'customer_map_screen.dart';

class CustomerDashboardScreen extends StatefulWidget {
  const CustomerDashboardScreen({Key? key}) : super(key: key);

  @override
  State<CustomerDashboardScreen> createState() => _CustomerDashboardScreenState();
}

class _CustomerDashboardScreenState extends State<CustomerDashboardScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  bool _isLocationEnabled = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  void _toggleLocationEnabled() {
    setState(() {
      _isLocationEnabled = !_isLocationEnabled;
    });
  }

  void _handleServiceHistory() {
    Navigator.pushNamed(context, AppRoutes.serviceHistoryScreen);
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.lightTheme.primaryColor,
                      AppTheme.lightTheme.primaryColor.withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.all(4.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Welcome Back!',
                                style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(height: 0.5.h),
                              Text(
                                'Ready to request a tow truck?',
                                style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                          IconButton(
                            onPressed: () {
                              Navigator.pushNamed(context, AppRoutes.settingsScreen);
                            },
                            icon: CustomIconWidget(
                              iconName: 'settings',
                              color: Colors.white,
                              size: 28,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Main Content
              Expanded(
                child: _isLocationEnabled 
                    ? CustomerMapScreen() 
                    : SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 4.h),

                            // Main Actions
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 4.w),
                              child: Column(
                                children: [
                                  // Enable Location Button
                                  Container(
                                    width: double.infinity,
                                    height: 8.h,
                                    child: ElevatedButton(
                                      onPressed: _toggleLocationEnabled,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red,
                                        foregroundColor: Colors.white,
                                        elevation: 8,
                                        shadowColor: Colors.red.withValues(alpha: 0.5),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(16),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.location_on,
                                            size: 32,
                                          ),
                                          SizedBox(width: 3.w),
                                          Text(
                                            'Enable Location',
                                            style: TextStyle(
                                              fontSize: 20.sp,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  SizedBox(height: 3.h),

                                  // View History Button
                                  Container(
                                    width: double.infinity,
                                    height: 6.h,
                                    child: OutlinedButton(
                                      onPressed: _handleServiceHistory,
                                      style: OutlinedButton.styleFrom(
                                        foregroundColor: AppTheme.lightTheme.primaryColor,
                                        side: BorderSide(
                                          color: AppTheme.lightTheme.primaryColor,
                                          width: 2,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.history,
                                            size: 24,
                                          ),
                                          SizedBox(width: 2.w),
                                          Text(
                                            'ดูประวัติ',
                                            style: TextStyle(
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            SizedBox(height: 4.h),

                            // Recent Orders
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 4.w),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Recent Orders',
                                    style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  SizedBox(height: 2.h),
                                  RecentOrdersWidget(
                                    onOrderTap: (order) {
                                      Navigator.pushNamed(
                                        context,
                                        AppRoutes.trackServiceScreen,
                                        arguments: {'orderId': order.id},
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),

                            SizedBox(height: 4.h),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
      // Add a floating action button to disable location when map is shown
      floatingActionButton: _isLocationEnabled ? FloatingActionButton(
        onPressed: _toggleLocationEnabled,
        backgroundColor: Colors.red,
        child: Icon(Icons.close, color: Colors.white),
      ) : null,
    );
  }
}
