import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../../core/app_export.dart';

class ServiceProgressWidget extends StatelessWidget {
  final OrderModel order;

  const ServiceProgressWidget({
    Key? key,
    required this.order,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final steps = _getProgressSteps();
    final currentStepIndex = _getCurrentStepIndex();

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: 'timeline',
                  color: AppTheme.lightTheme.primaryColor,
                  size: 20,
                ),
              ),
              SizedBox(width: 3.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Service Progress',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    _getStatusDescription(),
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: _getStatusColor(),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 3.w,
                  vertical: 1.h,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  order.status.toUpperCase(),
                  style: TextStyle(
                    color: _getStatusColor(),
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 3.h),

          // Progress Steps
          Column(
            children: List.generate(steps.length, (index) {
              final step = steps[index];
              final isCompleted = index < currentStepIndex;
              final isCurrent = index == currentStepIndex;
              final isUpcoming = index > currentStepIndex;

              return _buildProgressStep(
                step: step,
                isCompleted: isCompleted,
                isCurrent: isCurrent,
                isUpcoming: isUpcoming,
                isLast: index == steps.length - 1,
              );
            }),
          ),

          SizedBox(height: 2.h),

          // Estimated completion time
          if (order.isInProgress) ...[
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'schedule',
                    color: AppTheme.lightTheme.primaryColor,
                    size: 20,
                  ),
                  SizedBox(width: 2.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Estimated Completion',
                        style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.lightTheme.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        _getEstimatedCompletion(),
                        style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                          color: AppTheme.lightTheme.primaryColor,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressStep({
    required Map<String, dynamic> step,
    required bool isCompleted,
    required bool isCurrent,
    required bool isUpcoming,
    required bool isLast,
  }) {
    Color stepColor;
    if (isCompleted) {
      stepColor = Colors.green;
    } else if (isCurrent) {
      stepColor = AppTheme.lightTheme.primaryColor;
    } else {
      stepColor = Colors.grey;
    }

    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Step indicator
            Column(
              children: [
                Container(
                  width: 8.w,
                  height: 8.w,
                  decoration: BoxDecoration(
                    color: isCompleted ? stepColor : Colors.transparent,
                    border: Border.all(
                      color: stepColor,
                      width: isCompleted ? 0 : 2,
                    ),
                    borderRadius: BorderRadius.circular(4.w),
                  ),
                  child: Center(
                    child: isCompleted
                        ? CustomIconWidget(
                            iconName: 'check',
                            color: Colors.white,
                            size: 16,
                          )
                        : isCurrent
                            ? Container(
                                width: 3.w,
                                height: 3.w,
                                decoration: BoxDecoration(
                                  color: stepColor,
                                  borderRadius: BorderRadius.circular(1.5.w),
                                ),
                              )
                            : null,
                  ),
                ),
                if (!isLast)
                  Container(
                    width: 2,
                    height: 6.h,
                    color: isCompleted ? stepColor : Colors.grey.withValues(alpha: 0.3),
                  ),
              ],
            ),

            SizedBox(width: 4.w),

            // Step content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    step['title'],
                    style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isUpcoming 
                          ? AppTheme.lightTheme.colorScheme.onSurfaceVariant
                          : AppTheme.lightTheme.colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    step['description'],
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  if (step['timestamp'] != null) ...[
                    SizedBox(height: 0.5.h),
                    Text(
                      DateFormat('MMM dd, HH:mm').format(step['timestamp']),
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: stepColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
        if (!isLast) SizedBox(height: 1.h),
      ],
    );
  }

  List<Map<String, dynamic>> _getProgressSteps() {
    return [
      {
        'title': 'Order Placed',
        'description': 'Your service request has been submitted',
        'timestamp': order.createdAt,
      },
      {
        'title': 'Supplier Assigned',
        'description': 'A qualified supplier has accepted your request',
        'timestamp': order.acceptedAt,
      },
      {
        'title': 'Supplier En Route',
        'description': 'Supplier is on the way to your location',
        'timestamp': order.acceptedAt != null 
            ? order.acceptedAt!.add(const Duration(minutes: 5))
            : null,
      },
      {
        'title': 'Supplier Arrived',
        'description': 'Supplier has arrived at your location',
        'timestamp': null,
      },
      {
        'title': 'Service in Progress',
        'description': 'Work is being performed on your vehicle',
        'timestamp': null,
      },
      {
        'title': 'Service Completed',
        'description': 'Service has been completed successfully',
        'timestamp': order.completedAt,
      },
    ];
  }

  int _getCurrentStepIndex() {
    switch (order.status) {
      case AppConstants.orderStatusPending:
        return 0;
      case AppConstants.orderStatusAccepted:
        return 1;
      case AppConstants.orderStatusInProgress:
        return 2; // Supplier en route or working
      case AppConstants.orderStatusCompleted:
        return 5;
      case AppConstants.orderStatusCancelled:
      case AppConstants.orderStatusRejected:
        return 0;
      default:
        return 0;
    }
  }

  Color _getStatusColor() {
    switch (order.status) {
      case AppConstants.orderStatusPending:
        return Colors.orange;
      case AppConstants.orderStatusAccepted:
        return Colors.blue;
      case AppConstants.orderStatusInProgress:
        return Colors.purple;
      case AppConstants.orderStatusCompleted:
        return Colors.green;
      case AppConstants.orderStatusCancelled:
        return Colors.red;
      case AppConstants.orderStatusRejected:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getStatusDescription() {
    switch (order.status) {
      case AppConstants.orderStatusPending:
        return 'Waiting for supplier';
      case AppConstants.orderStatusAccepted:
        return 'Supplier assigned';
      case AppConstants.orderStatusInProgress:
        return 'Service in progress';
      case AppConstants.orderStatusCompleted:
        return 'Service completed';
      case AppConstants.orderStatusCancelled:
        return 'Order cancelled';
      case AppConstants.orderStatusRejected:
        return 'No supplier available';
      default:
        return 'Unknown status';
    }
  }

  String _getEstimatedCompletion() {
    final now = DateTime.now();
    final estimatedTime = now.add(const Duration(minutes: 30));
    return DateFormat('HH:mm').format(estimatedTime);
  }
}
