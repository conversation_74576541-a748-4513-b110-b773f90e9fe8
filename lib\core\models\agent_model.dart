import 'package:towtruck_pro/core/constants/app_constants.dart';

/// Agent model for customer service representatives
class AgentModel {
  final String id;
  final String userId;
  final String applicationId;
  final String status;
  final String? companyName;
  final String? businessLicense;
  final String? description;
  final double latitude;
  final double longitude;
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String phoneNumber;
  final double commissionRate;
  final double totalEarnings;
  final int totalOrders;
  final int completedOrders;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? approvedAt;
  final DateTime? updatedAt;
  final String? approvedBy;
  final String? rejectionReason;
  final AgentSettings? settings;

  AgentModel({
    required this.id,
    required this.userId,
    required this.applicationId,
    required this.status,
    this.companyName,
    this.businessLicense,
    this.description,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.phoneNumber,
    this.commissionRate = 0.10,
    this.totalEarnings = 0.0,
    this.totalOrders = 0,
    this.completedOrders = 0,
    this.isActive = true,
    required this.createdAt,
    this.approvedAt,
    this.updatedAt,
    this.approvedBy,
    this.rejectionReason,
    this.settings,
  });

  bool get isPending => status == AppConstants.agentStatusPending;
  bool get isApproved => status == AppConstants.agentStatusApproved;
  bool get isRejected => status == AppConstants.agentStatusRejected;
  bool get isSuspended => status == AppConstants.agentStatusSuspended;

  double get completionRate => totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0.0;

  factory AgentModel.fromJson(Map<String, dynamic> json) {
    return AgentModel(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      applicationId: json['application_id'] ?? '',
      status: json['status'] ?? AppConstants.agentStatusPending,
      companyName: json['company_name'],
      businessLicense: json['business_license'],
      description: json['description'],
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      zipCode: json['zip_code'] ?? '',
      phoneNumber: json['phone_number'] ?? '',
      commissionRate: json['commission_rate']?.toDouble() ?? 0.10,
      totalEarnings: json['total_earnings']?.toDouble() ?? 0.0,
      totalOrders: json['total_orders'] ?? 0,
      completedOrders: json['completed_orders'] ?? 0,
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      approvedAt: json['approved_at'] != null ? DateTime.parse(json['approved_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      approvedBy: json['approved_by'],
      rejectionReason: json['rejection_reason'],
      settings: json['settings'] != null ? AgentSettings.fromJson(json['settings']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'application_id': applicationId,
      'status': status,
      'company_name': companyName,
      'business_license': businessLicense,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'state': state,
      'zip_code': zipCode,
      'phone_number': phoneNumber,
      'commission_rate': commissionRate,
      'total_earnings': totalEarnings,
      'total_orders': totalOrders,
      'completed_orders': completedOrders,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'approved_at': approvedAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'approved_by': approvedBy,
      'rejection_reason': rejectionReason,
      'settings': settings?.toJson(),
    };
  }

  AgentModel copyWith({
    String? id,
    String? userId,
    String? applicationId,
    String? status,
    String? companyName,
    String? businessLicense,
    String? description,
    double? latitude,
    double? longitude,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? phoneNumber,
    double? commissionRate,
    double? totalEarnings,
    int? totalOrders,
    int? completedOrders,
    bool? isActive,
    DateTime? createdAt,
    DateTime? approvedAt,
    DateTime? updatedAt,
    String? approvedBy,
    String? rejectionReason,
    AgentSettings? settings,
  }) {
    return AgentModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      applicationId: applicationId ?? this.applicationId,
      status: status ?? this.status,
      companyName: companyName ?? this.companyName,
      businessLicense: businessLicense ?? this.businessLicense,
      description: description ?? this.description,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      commissionRate: commissionRate ?? this.commissionRate,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      totalOrders: totalOrders ?? this.totalOrders,
      completedOrders: completedOrders ?? this.completedOrders,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      approvedAt: approvedAt ?? this.approvedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      approvedBy: approvedBy ?? this.approvedBy,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      settings: settings ?? this.settings,
    );
  }
}

/// Agent application model
class AgentApplicationModel {
  final String id;
  final String userId;
  final String firstName;
  final String lastName;
  final String email;
  final String phoneNumber;
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String? companyName;
  final String? businessLicense;
  final String? description;
  final String? experienceYears;
  final List<String> documents;
  final String status;
  final DateTime createdAt;
  final DateTime? reviewedAt;
  final String? reviewedBy;
  final String? reviewNotes;

  AgentApplicationModel({
    required this.id,
    required this.userId,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phoneNumber,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    this.companyName,
    this.businessLicense,
    this.description,
    this.experienceYears,
    this.documents = const [],
    required this.status,
    required this.createdAt,
    this.reviewedAt,
    this.reviewedBy,
    this.reviewNotes,
  });

  bool get isPending => status == AppConstants.agentStatusPending;
  bool get isApproved => status == AppConstants.agentStatusApproved;
  bool get isRejected => status == AppConstants.agentStatusRejected;

  factory AgentApplicationModel.fromJson(Map<String, dynamic> json) {
    return AgentApplicationModel(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      email: json['email'] ?? '',
      phoneNumber: json['phone_number'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      zipCode: json['zip_code'] ?? '',
      companyName: json['company_name'],
      businessLicense: json['business_license'],
      description: json['description'],
      experienceYears: json['experience_years'],
      documents: List<String>.from(json['documents'] ?? []),
      status: json['status'] ?? AppConstants.agentStatusPending,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      reviewedAt: json['reviewed_at'] != null ? DateTime.parse(json['reviewed_at']) : null,
      reviewedBy: json['reviewed_by'],
      reviewNotes: json['review_notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone_number': phoneNumber,
      'address': address,
      'city': city,
      'state': state,
      'zip_code': zipCode,
      'company_name': companyName,
      'business_license': businessLicense,
      'description': description,
      'experience_years': experienceYears,
      'documents': documents,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'reviewed_at': reviewedAt?.toIso8601String(),
      'reviewed_by': reviewedBy,
      'review_notes': reviewNotes,
    };
  }
}

/// Agent settings model
class AgentSettings {
  final String agentId;
  final double serviceRadius;
  final bool notificationsEnabled;
  final bool autoAcceptOrders;
  final List<String> workingHours;
  final List<String> workingDays;
  final Map<String, dynamic> preferences;

  AgentSettings({
    required this.agentId,
    this.serviceRadius = 50.0,
    this.notificationsEnabled = true,
    this.autoAcceptOrders = false,
    this.workingHours = const [],
    this.workingDays = const [],
    this.preferences = const {},
  });

  factory AgentSettings.fromJson(Map<String, dynamic> json) {
    return AgentSettings(
      agentId: json['agent_id'] ?? '',
      serviceRadius: json['service_radius']?.toDouble() ?? 50.0,
      notificationsEnabled: json['notifications_enabled'] ?? true,
      autoAcceptOrders: json['auto_accept_orders'] ?? false,
      workingHours: List<String>.from(json['working_hours'] ?? []),
      workingDays: List<String>.from(json['working_days'] ?? []),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'agent_id': agentId,
      'service_radius': serviceRadius,
      'notifications_enabled': notificationsEnabled,
      'auto_accept_orders': autoAcceptOrders,
      'working_hours': workingHours,
      'working_days': workingDays,
      'preferences': preferences,
    };
  }

  AgentSettings copyWith({
    String? agentId,
    double? serviceRadius,
    bool? notificationsEnabled,
    bool? autoAcceptOrders,
    List<String>? workingHours,
    List<String>? workingDays,
    Map<String, dynamic>? preferences,
  }) {
    return AgentSettings(
      agentId: agentId ?? this.agentId,
      serviceRadius: serviceRadius ?? this.serviceRadius,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      autoAcceptOrders: autoAcceptOrders ?? this.autoAcceptOrders,
      workingHours: workingHours ?? this.workingHours,
      workingDays: workingDays ?? this.workingDays,
      preferences: preferences ?? this.preferences,
    );
  }
}
