import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:towtruck_pro/core/constants/app_constants.dart';
import 'package:towtruck_pro/core/services/storage_service.dart';

/// Notification service for handling push notifications and local notifications
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final StorageService _storageService = StorageService();

  String? _fcmToken;
  bool _isInitialized = false;

  /// Get FCM token
  String? get fcmToken => _fcmToken;

  /// Check if notifications are enabled
  bool get isInitialized => _isInitialized;

  /// Initialize notification service
  Future<bool> initialize() async {
    try {
      // Request permission for notifications
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus != AuthorizationStatus.authorized) {
        return false;
      }

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Get FCM token
      _fcmToken = await _firebaseMessaging.getToken();

      // Set up message handlers
      _setupMessageHandlers();

      // Subscribe to topics based on user role
      await _subscribeToTopics();

      _isInitialized = true;
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    await _createNotificationChannels();
  }

  /// Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    const orderChannel = AndroidNotificationChannel(
      'order_notifications',
      'Order Notifications',
      description: 'Notifications for order updates',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification_sound'),
    );

    const generalChannel = AndroidNotificationChannel(
      'general_notifications',
      'General Notifications',
      description: 'General app notifications',
      importance: Importance.defaultImportance,
    );

    const emergencyChannel = AndroidNotificationChannel(
      'emergency_notifications',
      'Emergency Notifications',
      description: 'Emergency and urgent notifications',
      importance: Importance.max,
      sound: RawResourceAndroidNotificationSound('emergency_sound'),
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(orderChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(generalChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(emergencyChannel);
  }

  /// Setup message handlers
  void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    _firebaseMessaging.getInitialMessage().then((message) {
      if (message != null) {
        _handleNotificationTap(message);
      }
    });

    // Handle token refresh
    _firebaseMessaging.onTokenRefresh.listen((token) {
      _fcmToken = token;
      _updateTokenOnServer(token);
    });
  }

  /// Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    await _showLocalNotification(message);
  }

  /// Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    // Handle background message processing
    // This function must be a top-level function
  }

  /// Handle notification tap
  void _handleNotificationTap(RemoteMessage message) {
    final data = message.data;
    final type = data['type'];

    switch (type) {
      case AppConstants.notificationTypeOrderRequest:
        _navigateToOrderDetails(data['order_id']);
        break;
      case AppConstants.notificationTypeOrderUpdate:
        _navigateToOrderDetails(data['order_id']);
        break;
      case AppConstants.notificationTypeAgentApplication:
        _navigateToAgentApplications();
        break;
      default:
        _navigateToHome();
    }
  }

  /// Handle local notification tap
  void _onNotificationTapped(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null) {
      // Parse payload and navigate accordingly
      _handleNotificationPayload(payload);
    }
  }

  /// Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;

    if (notification == null) return;

    final channelId = _getChannelId(data['type']);
    final importance = _getImportance(data['type']);

    const androidDetails = AndroidNotificationDetails(
      'default_channel',
      'Default Channel',
      channelDescription: 'Default notification channel',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      notification.title,
      notification.body,
      details,
      payload: _createPayload(data),
    );
  }

  /// Get notification channel ID based on type
  String _getChannelId(String? type) {
    switch (type) {
      case AppConstants.notificationTypeOrderRequest:
      case AppConstants.notificationTypeOrderUpdate:
        return 'order_notifications';
      case AppConstants.notificationTypeAgentApplication:
        return 'emergency_notifications';
      default:
        return 'general_notifications';
    }
  }

  /// Get notification importance based on type
  Importance _getImportance(String? type) {
    switch (type) {
      case AppConstants.notificationTypeOrderRequest:
        return Importance.max;
      case AppConstants.notificationTypeOrderUpdate:
        return Importance.high;
      case AppConstants.notificationTypeAgentApplication:
        return Importance.max;
      default:
        return Importance.defaultImportance;
    }
  }

  /// Create notification payload
  String _createPayload(Map<String, dynamic> data) {
    return data.entries.map((e) => '${e.key}=${e.value}').join('&');
  }

  /// Handle notification payload
  void _handleNotificationPayload(String payload) {
    final params = <String, String>{};
    for (final param in payload.split('&')) {
      final parts = param.split('=');
      if (parts.length == 2) {
        params[parts[0]] = parts[1];
      }
    }

    final type = params['type'];
    switch (type) {
      case AppConstants.notificationTypeOrderRequest:
      case AppConstants.notificationTypeOrderUpdate:
        _navigateToOrderDetails(params['order_id']);
        break;
      case AppConstants.notificationTypeAgentApplication:
        _navigateToAgentApplications();
        break;
      default:
        _navigateToHome();
    }
  }

  /// Subscribe to topics based on user role
  Future<void> _subscribeToTopics() async {
    final userRole = await _storageService.getString(AppConstants.keyUserRole);
    
    if (userRole != null) {
      await _firebaseMessaging.subscribeToTopic('all_users');
      await _firebaseMessaging.subscribeToTopic(userRole.toLowerCase());
      
      // Subscribe to location-based topics if needed
      final userId = await _storageService.getString(AppConstants.keyUserId);
      if (userId != null) {
        await _firebaseMessaging.subscribeToTopic('user_$userId');
      }
    }
  }

  /// Unsubscribe from topics
  Future<void> unsubscribeFromTopics() async {
    final userRole = await _storageService.getString(AppConstants.keyUserRole);
    
    if (userRole != null) {
      await _firebaseMessaging.unsubscribeFromTopic('all_users');
      await _firebaseMessaging.unsubscribeFromTopic(userRole.toLowerCase());
      
      final userId = await _storageService.getString(AppConstants.keyUserId);
      if (userId != null) {
        await _firebaseMessaging.unsubscribeFromTopic('user_$userId');
      }
    }
  }

  /// Update FCM token on server
  Future<void> _updateTokenOnServer(String token) async {
    // TODO: Implement API call to update token on server
    await _storageService.setString('fcm_token', token);
  }

  /// Send local notification
  Future<void> sendLocalNotification({
    required String title,
    required String body,
    String? payload,
    String channelId = 'general_notifications',
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'general_notifications',
      'General Notifications',
      channelDescription: 'General app notifications',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
    );

    const iosDetails = DarwinNotificationDetails();

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      details,
      payload: payload,
    );
  }

  /// Schedule notification
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'scheduled_notifications',
      'Scheduled Notifications',
      channelDescription: 'Scheduled notifications',
      importance: Importance.defaultImportance,
    );

    const iosDetails = DarwinNotificationDetails();

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.zonedSchedule(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      details,
      payload: payload,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );
  }

  /// Cancel notification
  Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// Navigation methods (to be implemented by UI layer)
  void _navigateToOrderDetails(String? orderId) {
    // TODO: Implement navigation to order details
  }

  void _navigateToAgentApplications() {
    // TODO: Implement navigation to agent applications
  }

  void _navigateToHome() {
    // TODO: Implement navigation to home
  }
}
