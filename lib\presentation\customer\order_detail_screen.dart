import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../core/app_export.dart';
import '../../core/services/order_service.dart';

class OrderDetailScreen extends StatefulWidget {
  final String orderId;

  const OrderDetailScreen({
    Key? key,
    required this.orderId,
  }) : super(key: key);

  @override
  State<OrderDetailScreen> createState() => _OrderDetailScreenState();
}

class _OrderDetailScreenState extends State<OrderDetailScreen> {
  OrderModel? _order;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrderDetail();
  }

  Future<void> _loadOrderDetail() async {
    try {
      final orderService = OrderService.instance;
      final order = await orderService.getOrderById(widget.orderId);
      
      if (mounted) {
        setState(() {
          _order = order;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('รายละเอียดคำขอ'),
        backgroundColor: AppTheme.lightTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.lightTheme.primaryColor,
                ),
              ),
            )
          : _order == null
              ? _buildNotFound()
              : _buildOrderDetail(),
    );
  }

  Widget _buildNotFound() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          SizedBox(height: 2.h),
          Text(
            'ไม่พบข้อมูลคำขอ',
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'คำขอนี้อาจถูกลบหรือไม่มีอยู่ในระบบ',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetail() {
    final order = _order!;
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Status Card
          _buildStatusCard(order),
          
          SizedBox(height: 3.h),
          
          // Order Information
          _buildInfoCard('ข้อมูลคำขอ', [
            _buildInfoRow('หมายเลขคำขอ', order.id),
            _buildInfoRow('ประเภทบริการ', _getServiceTypeName(order.serviceType)),
            _buildInfoRow('วันที่สร้าง', DateFormat('dd/MM/yyyy HH:mm').format(order.createdAt)),
            if (order.completedAt != null)
              _buildInfoRow('วันที่เสร็จสิ้น', DateFormat('dd/MM/yyyy HH:mm').format(order.completedAt!)),
          ]),
          
          SizedBox(height: 3.h),
          
          // Location Information
          _buildInfoCard('ข้อมูลตำแหน่ง', [
            _buildInfoRow('จุดรับ', order.pickupLocation.address),
            if (order.dropoffLocation != null)
              _buildInfoRow('จุดส่ง', order.dropoffLocation!.address),
          ]),
          
          SizedBox(height: 3.h),
          
          // Vehicle Information
          _buildInfoCard('ข้อมูลรถยนต์', [
            _buildInfoRow('ยี่ห้อ', order.vehicleInfo.make),
            if (order.vehicleInfo.model.isNotEmpty && order.vehicleInfo.model != 'ไม่ระบุ')
              _buildInfoRow('รุ่น', order.vehicleInfo.model),
            if (order.vehicleInfo.year != 'ไม่ระบุ')
              _buildInfoRow('ปี', order.vehicleInfo.year),
            if (order.vehicleInfo.color != 'ไม่ระบุ')
              _buildInfoRow('สี', order.vehicleInfo.color),
            if (order.vehicleInfo.licensePlate != 'ไม่ระบุ')
              _buildInfoRow('ทะเบียน', order.vehicleInfo.licensePlate),
          ]),
          
          SizedBox(height: 3.h),
          
          // Description
          if (order.description.isNotEmpty)
            _buildInfoCard('รายละเอียดเพิ่มเติม', [
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Text(
                  order.description,
                  style: AppTheme.lightTheme.textTheme.bodyMedium,
                ),
              ),
            ]),
          
          SizedBox(height: 3.h),
          
          // Price Information
          _buildInfoCard('ข้อมูลราคา', [
            if (order.estimatedPrice != null)
              _buildInfoRow('ราคาประเมิน', '฿${order.estimatedPrice!.toStringAsFixed(0)}'),
            if (order.finalPrice != null)
              _buildInfoRow('ราคาสุดท้าย', '฿${order.finalPrice!.toStringAsFixed(0)}', isHighlight: true),
          ]),
          
          SizedBox(height: 4.h),
          
          // Action Buttons
          if (order.status == AppConstants.orderStatusPending || 
              order.status == AppConstants.orderStatusConfirmed)
            _buildActionButtons(order),
        ],
      ),
    );
  }

  Widget _buildStatusCard(OrderModel order) {
    Color statusColor = _getStatusColor(order.status);
    String statusText = _getStatusText(order.status);
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            _getStatusIcon(order.status),
            size: 48,
            color: statusColor,
          ),
          SizedBox(height: 2.h),
          Text(
            statusText,
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            _getStatusDescription(order.status),
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Text(
              title,
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: AppTheme.lightTheme.primaryColor,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.all(4.w),
            child: Column(
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isHighlight = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 1.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 30.w,
            child: Text(
              label,
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                fontWeight: isHighlight ? FontWeight.w700 : FontWeight.w400,
                color: isHighlight ? AppTheme.lightTheme.primaryColor : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(OrderModel order) {
    return Column(
      children: [
        // Cancel Order Button
        SizedBox(
          width: double.infinity,
          height: 6.h,
          child: OutlinedButton(
            onPressed: () => _showCancelDialog(order),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: const BorderSide(color: Colors.red, width: 2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'ยกเลิกคำขอ',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                height: 1
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showCancelDialog(OrderModel order) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('ยกเลิกคำขอ'),
          content: const Text('คุณแน่ใจหรือไม่ที่จะยกเลิกคำขอนี้?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('ไม่'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);
                await _cancelOrder(order);
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('ใช่, ยกเลิก'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _cancelOrder(OrderModel order) async {
    try {
      final orderService = OrderService.instance;
      await orderService.updateOrderStatus(order.id, AppConstants.orderStatusCancelled);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('ยกเลิกคำขอเรียบร้อยแล้ว'),
            backgroundColor: Colors.green,
          ),
        );
        
        // Reload order detail
        _loadOrderDetail();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('เกิดข้อผิดพลาดในการยกเลิกคำขอ'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getServiceTypeName(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return 'รถลาก (Towing)';
      case AppConstants.serviceTypeJumpStart:
        return 'จั๊มแบตเตอรี่ (Jump Start)';
      case AppConstants.serviceTypeFlatTire:
        return 'เปลี่ยนยาง (Flat Tire)';
      case AppConstants.serviceTypeFuelDelivery:
        return 'ส่งน้ำมัน (Fuel Delivery)';
      case AppConstants.serviceTypeLockout:
        return 'ไขกุญแจ (Lockout)';
      default:
        return serviceType;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return Colors.orange;
      case AppConstants.orderStatusConfirmed:
        return Colors.blue;
      case AppConstants.orderStatusInProgress:
        return Colors.purple;
      case AppConstants.orderStatusCompleted:
        return Colors.green;
      case AppConstants.orderStatusCancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return 'รอการยืนยัน';
      case AppConstants.orderStatusConfirmed:
        return 'ยืนยันแล้ว';
      case AppConstants.orderStatusInProgress:
        return 'กำลังดำเนินการ';
      case AppConstants.orderStatusCompleted:
        return 'เสร็จสิ้น';
      case AppConstants.orderStatusCancelled:
        return 'ยกเลิกแล้ว';
      default:
        return status;
    }
  }

  String _getStatusDescription(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return 'คำขอของคุณอยู่ในระหว่างการตรวจสอบ';
      case AppConstants.orderStatusConfirmed:
        return 'คำขอได้รับการยืนยันแล้ว กำลังหาผู้ให้บริการ';
      case AppConstants.orderStatusInProgress:
        return 'ผู้ให้บริการกำลังเดินทางไปยังตำแหน่งของคุณ';
      case AppConstants.orderStatusCompleted:
        return 'บริการเสร็จสิ้นเรียบร้อยแล้ว';
      case AppConstants.orderStatusCancelled:
        return 'คำขอนี้ถูกยกเลิกแล้ว';
      default:
        return '';
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return Icons.hourglass_empty;
      case AppConstants.orderStatusConfirmed:
        return Icons.check_circle_outline;
      case AppConstants.orderStatusInProgress:
        return Icons.local_shipping;
      case AppConstants.orderStatusCompleted:
        return Icons.check_circle;
      case AppConstants.orderStatusCancelled:
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }
}
