/// Application constants for TowTruck Pro
class AppConstants {
  AppConstants._();

  // App Information
  static const String appName = 'TowTruck Pro';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Professional Tow Truck Service Application';

  // API Configuration
  static const String baseUrl = 'https://api.towtruckpro.com';
  static const String apiVersion = 'v1';
  static const Duration apiTimeout = Duration(seconds: 30);

  // Database Configuration
  static const String databaseName = 'towtruck_pro.db';
  static const int databaseVersion = 1;

  // SharedPreferences Keys
  static const String keyIsFirstTime = 'is_first_time';
  static const String keyUserToken = 'user_token';
  static const String keyUserRole = 'user_role';
  static const String keyUserId = 'user_id';
  static const String keyUserEmail = 'user_email';
  static const String keyRememberCredentials = 'remember_credentials';
  static const String keyBiometricEnabled = 'biometric_enabled';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  static const String keyLocationPermissionGranted = 'location_permission_granted';

  // User Roles
  static const String roleCustomer = 'Customer';
  static const String roleSupplier = 'Supplier';
  static const String roleAgent = 'Agent';
  static const String roleAdmin = 'Admin';

  // Service Types
  static const String serviceTypeTowing = 'Towing';
  static const String serviceTypeJumpStart = 'Jump Start';
  static const String serviceTypeFlatTire = 'Flat Tire';
  static const String serviceTypeFuelDelivery = 'Fuel Delivery';
  static const String serviceTypeLockout = 'Lockout';
  static const String serviceTypeWinchOut = 'Winch Out';

  // Order Status
  static const String orderStatusPending = 'Pending';
  static const String orderStatusAccepted = 'Accepted';
  static const String orderStatusInProgress = 'In Progress';
  static const String orderStatusCompleted = 'Completed';
  static const String orderStatusCancelled = 'Cancelled';
  static const String orderStatusRejected = 'Rejected';

  // Agent Application Status
  static const String agentStatusPending = 'Pending';
  static const String agentStatusApproved = 'Approved';
  static const String agentStatusRejected = 'Rejected';
  static const String agentStatusSuspended = 'Suspended';

  // Supplier Status
  static const String supplierStatusOnline = 'Online';
  static const String supplierStatusOffline = 'Offline';
  static const String supplierStatusBusy = 'Busy';

  // Notification Types
  static const String notificationTypeOrderRequest = 'order_request';
  static const String notificationTypeOrderUpdate = 'order_update';
  static const String notificationTypeAgentApplication = 'agent_application';
  static const String notificationTypeSystemUpdate = 'system_update';

  // Map Configuration
  static const double defaultLatitude = 13.7563;
  static const double defaultLongitude = 100.5018; // Bangkok, Thailand
  static const double defaultZoom = 15.0;
  static const double searchRadius = 50.0; // kilometers

  // Commission Rates
  static const double agentCommissionRate = 0.10; // 10%
  static const double platformFeeRate = 0.05; // 5%

  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int otpLength = 6;
  static const Duration otpValidityDuration = Duration(minutes: 5);

  // File Upload
  static const int maxImageSizeBytes = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png'];

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Error Messages
  static const String errorNetworkConnection = 'No internet connection';
  static const String errorServerError = 'Server error occurred';
  static const String errorUnauthorized = 'Unauthorized access';
  static const String errorInvalidCredentials = 'Invalid email or password';
  static const String errorLocationPermission = 'Location permission required';
  static const String errorCameraPermission = 'Camera permission required';
  static const String errorStoragePermission = 'Storage permission required';

  // Success Messages
  static const String successLoginMessage = 'Login successful';
  static const String successRegistrationMessage = 'Registration successful';
  static const String successOrderCreated = 'Order created successfully';
  static const String successOrderUpdated = 'Order updated successfully';
  static const String successProfileUpdated = 'Profile updated successfully';

  // Regex Patterns
  static const String emailPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String phonePattern = r'^[0-9]{10}$';
  static const String licensePlatePattern = r'^[A-Z0-9]{1,8}$';

  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';

  // Currency
  static const String currency = 'THB';
  static const String currencySymbol = '฿';

  static var userRoleCustomer;
}
