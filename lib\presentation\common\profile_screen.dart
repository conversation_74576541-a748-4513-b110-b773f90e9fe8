import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  UserModel? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadUserProfile();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  Future<void> _loadUserProfile() async {
    // Simulate loading user profile
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _currentUser = UserModel(
        id: 'user1',
        email: '<EMAIL>',
        phoneNumber: '+66 81-234-5678',
        firstName: 'John',
        lastName: 'Doe',
        role: AppConstants.userRoleCustomer,
        isVerified: true,
        createdAt: DateTime.now().subtract(const Duration(days: 180)),

      );
      _isLoading = false;
    });
  }

  void _handleEditProfile() {
    Navigator.pushNamed(context, '/edit-profile-screen');
  }

  void _handleChangePassword() {
    Navigator.pushNamed(context, '/change-password-screen');
  }

  void _handleNotificationSettings() {
    Navigator.pushNamed(context, '/notification-settings-screen');
  }

  void _handlePrivacySettings() {
    Navigator.pushNamed(context, '/privacy-settings-screen');
  }

  void _handleHelpSupport() {
    Navigator.pushNamed(context, '/help-support-screen');
  }

  void _handleAbout() {
    Navigator.pushNamed(context, '/about-screen');
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushNamedAndRemoveUntil(
                context,
                AppRoutes.loginScreen,
                (route) => false,
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Profile'),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back_ios',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _handleEditProfile,
            icon: CustomIconWidget(
              iconName: 'edit',
              color: AppTheme.lightTheme.primaryColor,
              size: 24,
            ),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Column(
                  children: [
                    // Profile header
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(6.w),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppTheme.lightTheme.primaryColor,
                            AppTheme.lightTheme.primaryColor.withValues(alpha: 0.8),
                          ],
                        ),
                      ),
                      child: Column(
                        children: [
                          // Profile avatar
                          Container(
                            width: 25.w,
                            height: 25.w,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12.5.w),
                              border: Border.all(
                                color: Colors.white,
                                width: 3,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                '${_currentUser?.firstName.substring(0, 1)}${_currentUser?.lastName.substring(0, 1)}',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                          ),

                          SizedBox(height: 2.h),

                          // User name
                          Text(
                            '${_currentUser?.firstName} ${_currentUser?.lastName}',
                            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w700,
                            ),
                          ),

                          SizedBox(height: 0.5.h),

                          // User role
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 3.w,
                              vertical: 1.h,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CustomIconWidget(
                                  iconName: _getRoleIcon(),
                                  color: Colors.white,
                                  size: 16,
                                ),
                                SizedBox(width: 1.w),
                                Text(
                                  _currentUser?.role.toUpperCase() ?? '',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: 1.h),

                          // Verification status
                          if (_currentUser?.isVerified == true)
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomIconWidget(
                                  iconName: 'verified',
                                  color: Colors.white,
                                  size: 16,
                                ),
                                SizedBox(width: 1.w),
                                Text(
                                  'Verified Account',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),

                    SizedBox(height: 3.h),

                    // Profile information
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Account Information',
                            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),

                          SizedBox(height: 2.h),

                          // Email
                          _buildInfoCard(
                            icon: 'email',
                            title: 'Email',
                            value: _currentUser?.email ?? '',
                            onTap: null,
                          ),

                          SizedBox(height: 2.h),

                          // Phone
                          _buildInfoCard(
                            icon: 'phone',
                            title: 'Phone Number',
                            value: _currentUser?.phoneNumber ?? '',
                            onTap: null,
                          ),

                          SizedBox(height: 2.h),

                          // Member since
                          _buildInfoCard(
                            icon: 'schedule',
                            title: 'Member Since',
                            value: _formatDate(_currentUser?.createdAt),
                            onTap: null,
                          ),

                          SizedBox(height: 3.h),

                          // Settings section
                          Text(
                            'Settings',
                            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),

                          SizedBox(height: 2.h),

                          // Settings options
                          _buildSettingsCard(
                            icon: 'lock',
                            title: 'Change Password',
                            subtitle: 'Update your password',
                            onTap: _handleChangePassword,
                          ),

                          SizedBox(height: 2.h),

                          _buildSettingsCard(
                            icon: 'notifications',
                            title: 'Notifications',
                            subtitle: 'Manage notification preferences',
                            onTap: _handleNotificationSettings,
                          ),

                          SizedBox(height: 2.h),

                          _buildSettingsCard(
                            icon: 'privacy_tip',
                            title: 'Privacy',
                            subtitle: 'Privacy and security settings',
                            onTap: _handlePrivacySettings,
                          ),

                          SizedBox(height: 3.h),

                          // Support section
                          Text(
                            'Support',
                            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),

                          SizedBox(height: 2.h),

                          _buildSettingsCard(
                            icon: 'help',
                            title: 'Help & Support',
                            subtitle: 'Get help and contact support',
                            onTap: _handleHelpSupport,
                          ),

                          SizedBox(height: 2.h),

                          _buildSettingsCard(
                            icon: 'info',
                            title: 'About',
                            subtitle: 'App version and information',
                            onTap: _handleAbout,
                          ),

                          SizedBox(height: 4.h),

                          // Logout button
                          SizedBox(
                            width: double.infinity,
                            height: 6.h,
                            child: OutlinedButton.icon(
                              onPressed: _handleLogout,
                              icon: CustomIconWidget(
                                iconName: 'logout',
                                color: Colors.red,
                                size: 20,
                              ),
                              label: Text(
                                'Logout',
                                style: TextStyle(
                                  color: Colors.red,
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              style: OutlinedButton.styleFrom(
                                side: const BorderSide(color: Colors.red, width: 2),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),

                          SizedBox(height: 4.h),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildInfoCard({
    required String icon,
    required String title,
    required String value,
    VoidCallback? onTap,
  }) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(2.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: CustomIconWidget(
              iconName: icon,
              color: AppTheme.lightTheme.primaryColor,
              size: 20,
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  value,
                  style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          if (onTap != null)
            CustomIconWidget(
              iconName: 'arrow_forward_ios',
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              size: 16,
            ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard({
    required String icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(2.w),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomIconWidget(
                iconName: icon,
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                size: 20,
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            CustomIconWidget(
              iconName: 'arrow_forward_ios',
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  String _getRoleIcon() {
    switch (_currentUser?.role) {
      case AppConstants.userRoleCustomer:
        return 'person';
      case AppConstants.userRoleSupplier:
        return 'local_shipping';
      case AppConstants.userRoleAgent:
        return 'business_center';
      case AppConstants.userRoleAdmin:
        return 'admin_panel_settings';
      default:
        return 'person';
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '';
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }
}
