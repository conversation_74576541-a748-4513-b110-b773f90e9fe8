# 🚗 Vehicle Info Parsing Test

## 📝 **ตัวอย่างการกรอกข้อมูลรถ:**

### ✅ **รูปแบบที่ถูกต้อง:**
```
Toyota Camry สีขาว ทะเบียน กข-1234
Honda Civic สีดำ ทะเบียน นม-5678
Mazda 3 สีแดง ทะเบียน บจ-9012
BMW X5 สีเงิน ทะเบียน กก-1111
Mercedes-Benz C200 สีน้ำเงิน ทะเบียน นน-2222
```

### 🔍 **การแยกข้อมูล (Parsing Logic):**

#### **Input:** `"Toyota Camry สีขาว ทะเบียน กข-1234"`
```dart
make: "Toyota"
model: "Camry" 
color: "ขาว"
licensePlate: "กข-1234"
year: "ไม่ระบุ" (default)
```

#### **Input:** `"Honda Civic สีดำ ทะเบียน นม-5678"`
```dart
make: "Honda"
model: "Civic"
color: "ดำ" 
licensePlate: "นม-5678"
year: "ไม่ระบุ" (default)
```

#### **Input:** `"BMW X5 สีเงิน"`
```dart
make: "BMW"
model: "X5"
color: "เงิน"
licensePlate: "ไม่ระบุ" (no license plate provided)
year: "ไม่ระบุ" (default)
```

#### **Input:** `"Toyota"`
```dart
make: "Toyota"
model: "" (empty)
color: "ไม่ระบุ" (default)
licensePlate: "ไม่ระบุ" (default)
year: "ไม่ระบุ" (default)
```

## 🎯 **Parsing Algorithm:**

### 1️⃣ **Split by spaces:**
```dart
final parts = vehicleText.split(' ');
// "Toyota Camry สีขาว ทะเบียน กข-1234"
// → ["Toyota", "Camry", "สีขาว", "ทะเบียน", "กข-1234"]
```

### 2️⃣ **Extract Make (First word):**
```dart
make = parts[0]; // "Toyota"
```

### 3️⃣ **Extract Model (Words before "สี" or "ทะเบียน"):**
```dart
List<String> modelParts = [];
for (int i = 1; i < parts.length; i++) {
  if (parts[i].startsWith('สี') || parts[i] == 'ทะเบียน') break;
  modelParts.add(parts[i]);
}
model = modelParts.join(' '); // "Camry"
```

### 4️⃣ **Extract Color (After "สี"):**
```dart
for (int i = 0; i < parts.length; i++) {
  if (parts[i].startsWith('สี')) {
    color = parts[i].substring(2); // Remove "สี" → "ขาว"
    break;
  }
}
```

### 5️⃣ **Extract License Plate (After "ทะเบียน"):**
```dart
for (int i = 0; i < parts.length; i++) {
  if (parts[i] == 'ทะเบียน' && i + 1 < parts.length) {
    licensePlate = parts.skip(i + 1).join(' '); // "กข-1234"
    break;
  }
}
```

## 📱 **Display Logic:**

### 🏷️ **Order Detail Screen:**
```dart
// แสดงเฉพาะข้อมูลที่ไม่ใช่ "ไม่ระบุ"
if (order.vehicleInfo.make != 'ไม่ระบุ')
  _buildInfoRow('ยี่ห้อ', order.vehicleInfo.make),
if (order.vehicleInfo.model.isNotEmpty && order.vehicleInfo.model != 'ไม่ระบุ')
  _buildInfoRow('รุ่น', order.vehicleInfo.model),
if (order.vehicleInfo.color != 'ไม่ระบุ')
  _buildInfoRow('สี', order.vehicleInfo.color),
if (order.vehicleInfo.licensePlate != 'ไม่ระบุ')
  _buildInfoRow('ทะเบียน', order.vehicleInfo.licensePlate),
```

### 📋 **Service History & Recent Orders:**
```dart
String _getVehicleDisplayText(VehicleInfo vehicleInfo) {
  List<String> parts = [];
  
  if (vehicleInfo.make != 'ไม่ระบุ') parts.add(vehicleInfo.make);
  if (vehicleInfo.model.isNotEmpty && vehicleInfo.model != 'ไม่ระบุ') parts.add(vehicleInfo.model);
  if (vehicleInfo.color != 'ไม่ระบุ') parts.add('สี${vehicleInfo.color}');
  if (vehicleInfo.licensePlate != 'ไม่ระบุ') parts.add('(${vehicleInfo.licensePlate})');
  
  return parts.isNotEmpty ? parts.join(' ') : 'ข้อมูลรถยนต์';
}
```

## 🧪 **Test Cases:**

### ✅ **Expected Results:**

| Input | Make | Model | Color | License Plate | Display |
|-------|------|-------|-------|---------------|---------|
| `Toyota Camry สีขาว ทะเบียน กข-1234` | Toyota | Camry | ขาว | กข-1234 | `Toyota Camry สีขาว (กข-1234)` |
| `Honda Civic สีดำ` | Honda | Civic | ดำ | ไม่ระบุ | `Honda Civic สีดำ` |
| `BMW X5 ทะเบียน นน-2222` | BMW | X5 | ไม่ระบุ | นน-2222 | `BMW X5 (นน-2222)` |
| `Toyota` | Toyota | | ไม่ระบุ | ไม่ระบุ | `Toyota` |
| `` (empty) | ไม่ระบุ | | ไม่ระบุ | ไม่ระบุ | `ข้อมูลรถยนต์` |

## 🎯 **Benefits:**

### ✅ **Accurate Data:**
- แยกข้อมูลรถยนต์ได้ถูกต้องตามที่ผู้ใช้กรอก
- ไม่มีข้อมูล default ที่ผิดพลาด (เช่น year: "2023", color: "Unknown")

### ✅ **Clean Display:**
- แสดงเฉพาะข้อมูลที่มีจริง
- ซ่อนข้อมูลที่เป็น "ไม่ระบุ" หรือ empty

### ✅ **User-Friendly:**
- รองรับรูปแบบการกรอกที่หลากหลาย
- แสดงผลในรูปแบบที่อ่านง่าย

### ✅ **Consistent:**
- ใช้ logic เดียวกันใน Order Detail, Service History, และ Recent Orders
- ข้อมูลสอดคล้องกันทุกหน้า

**Vehicle Info Parsing System พร้อมใช้งาน!** 🚗✨
