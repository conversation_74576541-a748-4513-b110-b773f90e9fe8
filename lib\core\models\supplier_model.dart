import 'package:towtruck_pro/core/constants/app_constants.dart';

/// Supplier model for tow truck service providers
class SupplierModel {
  final String id;
  final String userId;
  final String companyName;
  final String businessLicense;
  final String? description;
  final String status;
  final double latitude;
  final double longitude;
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String phoneNumber;
  final String? website;
  final List<String> serviceTypes;
  final List<VehicleModel> vehicles;
  final double rating;
  final int totalJobs;
  final int completedJobs;
  final double walletBalance;
  final bool isVerified;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final SupplierSettings? settings;

  SupplierModel({
    required this.id,
    required this.userId,
    required this.companyName,
    required this.businessLicense,
    this.description,
    required this.status,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.phoneNumber,
    this.website,
    this.serviceTypes = const [],
    this.vehicles = const [],
    this.rating = 0.0,
    this.totalJobs = 0,
    this.completedJobs = 0,
    this.walletBalance = 0.0,
    this.isVerified = false,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.settings,
  });

  bool get isOnline => status == AppConstants.supplierStatusOnline;
  bool get isOffline => status == AppConstants.supplierStatusOffline;
  bool get isBusy => status == AppConstants.supplierStatusBusy;

  double get completionRate => totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0.0;

  factory SupplierModel.fromJson(Map<String, dynamic> json) {
    return SupplierModel(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      companyName: json['company_name'] ?? '',
      businessLicense: json['business_license'] ?? '',
      description: json['description'],
      status: json['status'] ?? AppConstants.supplierStatusOffline,
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      zipCode: json['zip_code'] ?? '',
      phoneNumber: json['phone_number'] ?? '',
      website: json['website'],
      serviceTypes: List<String>.from(json['service_types'] ?? []),
      vehicles: (json['vehicles'] as List<dynamic>?)
          ?.map((e) => VehicleModel.fromJson(e))
          .toList() ?? [],
      rating: json['rating']?.toDouble() ?? 0.0,
      totalJobs: json['total_jobs'] ?? 0,
      completedJobs: json['completed_jobs'] ?? 0,
      isVerified: json['is_verified'] ?? false,
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      settings: json['settings'] != null ? SupplierSettings.fromJson(json['settings']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'company_name': companyName,
      'business_license': businessLicense,
      'description': description,
      'status': status,
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'state': state,
      'zip_code': zipCode,
      'phone_number': phoneNumber,
      'website': website,
      'service_types': serviceTypes,
      'vehicles': vehicles.map((e) => e.toJson()).toList(),
      'rating': rating,
      'total_jobs': totalJobs,
      'completed_jobs': completedJobs,
      'is_verified': isVerified,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'settings': settings?.toJson(),
    };
  }

  SupplierModel copyWith({
    String? id,
    String? userId,
    String? companyName,
    String? businessLicense,
    String? description,
    String? status,
    double? latitude,
    double? longitude,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? phoneNumber,
    String? website,
    List<String>? serviceTypes,
    List<VehicleModel>? vehicles,
    double? rating,
    int? totalJobs,
    int? completedJobs,
    double? walletBalance,
    bool? isVerified,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    SupplierSettings? settings,
  }) {
    return SupplierModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      companyName: companyName ?? this.companyName,
      businessLicense: businessLicense ?? this.businessLicense,
      description: description ?? this.description,
      status: status ?? this.status,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      website: website ?? this.website,
      serviceTypes: serviceTypes ?? this.serviceTypes,
      vehicles: vehicles ?? this.vehicles,
      rating: rating ?? this.rating,
      totalJobs: totalJobs ?? this.totalJobs,
      completedJobs: completedJobs ?? this.completedJobs,
      walletBalance: walletBalance ?? this.walletBalance,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      settings: settings ?? this.settings,
    );
  }
}

/// Vehicle model for supplier's tow trucks
class VehicleModel {
  final String id;
  final String supplierId;
  final String make;
  final String model;
  final String year;
  final String licensePlate;
  final String vehicleType;
  final String color;
  final double capacity;
  final bool isActive;
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime? updatedAt;

  VehicleModel({
    required this.id,
    required this.supplierId,
    required this.make,
    required this.model,
    required this.year,
    required this.licensePlate,
    required this.vehicleType,
    required this.color,
    required this.capacity,
    this.isActive = true,
    this.imageUrl,
    required this.createdAt,
    this.updatedAt,
  });

  factory VehicleModel.fromJson(Map<String, dynamic> json) {
    return VehicleModel(
      id: json['id'] ?? '',
      supplierId: json['supplier_id'] ?? '',
      make: json['make'] ?? '',
      model: json['model'] ?? '',
      year: json['year'] ?? '',
      licensePlate: json['license_plate'] ?? '',
      vehicleType: json['vehicle_type'] ?? '',
      color: json['color'] ?? '',
      capacity: json['capacity']?.toDouble() ?? 0.0,
      isActive: json['is_active'] ?? true,
      imageUrl: json['image_url'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'supplier_id': supplierId,
      'make': make,
      'model': model,
      'year': year,
      'license_plate': licensePlate,
      'vehicle_type': vehicleType,
      'color': color,
      'capacity': capacity,
      'is_active': isActive,
      'image_url': imageUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  VehicleModel copyWith({
    String? id,
    String? supplierId,
    String? make,
    String? model,
    String? year,
    String? licensePlate,
    String? vehicleType,
    String? color,
    double? capacity,
    bool? isActive,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VehicleModel(
      id: id ?? this.id,
      supplierId: supplierId ?? this.supplierId,
      make: make ?? this.make,
      model: model ?? this.model,
      year: year ?? this.year,
      licensePlate: licensePlate ?? this.licensePlate,
      vehicleType: vehicleType ?? this.vehicleType,
      color: color ?? this.color,
      capacity: capacity ?? this.capacity,
      isActive: isActive ?? this.isActive,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Supplier settings model
class SupplierSettings {
  final String supplierId;
  final double serviceRadius;
  final bool autoAcceptOrders;
  final bool notificationsEnabled;
  final Map<String, double> servicePricing;
  final List<String> workingHours;
  final List<String> workingDays;

  SupplierSettings({
    required this.supplierId,
    this.serviceRadius = 50.0,
    this.autoAcceptOrders = false,
    this.notificationsEnabled = true,
    this.servicePricing = const {},
    this.workingHours = const [],
    this.workingDays = const [],
  });

  factory SupplierSettings.fromJson(Map<String, dynamic> json) {
    return SupplierSettings(
      supplierId: json['supplier_id'] ?? '',
      serviceRadius: json['service_radius']?.toDouble() ?? 50.0,
      autoAcceptOrders: json['auto_accept_orders'] ?? false,
      notificationsEnabled: json['notifications_enabled'] ?? true,
      servicePricing: Map<String, double>.from(json['service_pricing'] ?? {}),
      workingHours: List<String>.from(json['working_hours'] ?? []),
      workingDays: List<String>.from(json['working_days'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'supplier_id': supplierId,
      'service_radius': serviceRadius,
      'auto_accept_orders': autoAcceptOrders,
      'notifications_enabled': notificationsEnabled,
      'service_pricing': servicePricing,
      'working_hours': workingHours,
      'working_days': workingDays,
    };
  }

  SupplierSettings copyWith({
    String? supplierId,
    double? serviceRadius,
    bool? autoAcceptOrders,
    bool? notificationsEnabled,
    Map<String, double>? servicePricing,
    List<String>? workingHours,
    List<String>? workingDays,
  }) {
    return SupplierSettings(
      supplierId: supplierId ?? this.supplierId,
      serviceRadius: serviceRadius ?? this.serviceRadius,
      autoAcceptOrders: autoAcceptOrders ?? this.autoAcceptOrders,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      servicePricing: servicePricing ?? this.servicePricing,
      workingHours: workingHours ?? this.workingHours,
      workingDays: workingDays ?? this.workingDays,
    );
  }
}
