import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'dart:async';

import '../../core/app_export.dart';
import 'widgets/service_progress_widget.dart';
import 'widgets/supplier_info_widget.dart';
import 'widgets/live_map_widget.dart';

class TrackServiceScreen extends StatefulWidget {
  const TrackServiceScreen({Key? key}) : super(key: key);

  @override
  State<TrackServiceScreen> createState() => _TrackServiceScreenState();
}

class _TrackServiceScreenState extends State<TrackServiceScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  String? _orderId;
  OrderModel? _order;
  SupplierModel? _supplier;
  bool _isLoading = true;
  Timer? _locationTimer;
  
  // Mock real-time location data
  double _supplierLat = 13.7563;
  double _supplierLng = 100.5018;
  double _estimatedArrival = 15.0; // minutes

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _getOrderId();
    _loadOrderData();
    _startLocationTracking();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  void _getOrderId() {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      _orderId = args['orderId'];
    }
  }

  Future<void> _loadOrderData() async {
    if (_orderId == null) return;

    // Simulate loading order data
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _order = OrderModel(
        id: _orderId!,
        customerId: 'customer1',
        supplierId: 'supplier1',
        serviceType: AppConstants.serviceTypeTowing,
        status: AppConstants.orderStatusInProgress,
        description: 'Car breakdown on highway',
        pickupLocation: LocationInfo(
          latitude: 13.7563,
          longitude: 100.5018,
          address: 'Sukhumvit Road, Bangkok',
        ),
        vehicleInfo: VehicleInfo(
          make: 'Toyota',
          model: 'Camry',
          year: '2020',
          color: 'White',
          licensePlate: 'ABC-1234',
        ),
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        acceptedAt: DateTime.now().subtract(const Duration(minutes: 45)),
        estimatedPrice: 1500.0,
      );

      _supplier = SupplierModel(
        id: 'supplier1',
        userId: 'user1',
        companyName: 'Bangkok Tow Service',
        businessLicense: 'BTS-2024-001',
        status: AppConstants.supplierStatusOnline,
        latitude: _supplierLat,
        longitude: _supplierLng,
        address: '123 Sukhumvit Road',
        city: 'Bangkok',
        state: 'Bangkok',
        zipCode: '10110',
        phoneNumber: '02-123-4567',
        serviceTypes: [AppConstants.serviceTypeTowing],
        rating: 4.8,
        totalJobs: 156,
        completedJobs: 148,
        isVerified: true,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
      );

      _isLoading = false;
    });
  }

  void _startLocationTracking() {
    _locationTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      // Simulate supplier moving towards customer
      setState(() {
        _supplierLat += 0.0001;
        _supplierLng += 0.0001;
        _estimatedArrival = (_estimatedArrival - 0.5).clamp(0.0, 60.0);
      });
    });
  }

  void _handleCallSupplier() {
    if (_supplier != null) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Call Supplier'),
          content: Text('Call ${_supplier!.companyName}?\n${_supplier!.phoneNumber}'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
                // TODO: Implement call functionality
              },
              icon: CustomIconWidget(
                iconName: 'phone',
                color: Colors.white,
                size: 16,
              ),
              label: const Text('Call Now'),
            ),
          ],
        ),
      );
    }
  }

  void _handleCancelOrder() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: const Text('Are you sure you want to cancel this order? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Order cancelled successfully'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  void _handleShareLocation() {
    // TODO: Implement share location functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Location shared with supplier'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _locationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Track Service'),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back_ios',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _handleShareLocation,
            icon: CustomIconWidget(
              iconName: 'share_location',
              color: AppTheme.lightTheme.primaryColor,
              size: 24,
            ),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadOrderData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      // Live Map
                      if (_order != null && _supplier != null)
                        LiveMapWidget(
                          order: _order!,
                          supplierLocation: LocationInfo(
                            latitude: _supplierLat,
                            longitude: _supplierLng,
                            address: 'Moving towards you',
                          ),
                          estimatedArrival: _estimatedArrival,
                        ),

                      SizedBox(height: 2.h),

                      // Service Progress
                      if (_order != null)
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4.w),
                          child: ServiceProgressWidget(order: _order!),
                        ),

                      SizedBox(height: 3.h),

                      // Supplier Info
                      if (_supplier != null)
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4.w),
                          child: SupplierInfoWidget(
                            supplier: _supplier!,
                            onCallSupplier: _handleCallSupplier,
                          ),
                        ),

                      SizedBox(height: 3.h),

                      // Order Details
                      if (_order != null)
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4.w),
                          child: _buildOrderDetails(),
                        ),

                      SizedBox(height: 3.h),

                      // Action Buttons
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 4.w),
                        child: Column(
                          children: [
                            // Call Supplier Button
                            SizedBox(
                              width: double.infinity,
                              height: 6.h,
                              child: ElevatedButton.icon(
                                onPressed: _handleCallSupplier,
                                icon: CustomIconWidget(
                                  iconName: 'phone',
                                  color: Colors.white,
                                  size: 20,
                                ),
                                label: Text(
                                  'Call Supplier',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),

                            SizedBox(height: 2.h),

                            // Cancel Order Button
                            if (_order!.canBeCancelled)
                              SizedBox(
                                width: double.infinity,
                                height: 6.h,
                                child: OutlinedButton.icon(
                                  onPressed: _handleCancelOrder,
                                  icon: CustomIconWidget(
                                    iconName: 'cancel',
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                  label: Text(
                                    'Cancel Order',
                                    style: TextStyle(
                                      color: Colors.red,
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  style: OutlinedButton.styleFrom(
                                    side: const BorderSide(color: Colors.red, width: 2),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      SizedBox(height: 4.h),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildOrderDetails() {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Details',
            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          
          SizedBox(height: 2.h),
          
          _buildDetailRow('Order ID', '#${_order!.id.length > 8 ? _order!.id.substring(0, 8) : _order!.id}'),
          _buildDetailRow('Service Type', _order!.serviceType),
          _buildDetailRow('Vehicle', '${_order!.vehicleInfo.make} ${_order!.vehicleInfo.model}'),
          _buildDetailRow('License Plate', _order!.vehicleInfo.licensePlate),
          _buildDetailRow('Pickup Location', _order!.pickupLocation.address),
          if (_order!.description.isNotEmpty)
            _buildDetailRow('Description', _order!.description),
          _buildDetailRow('Estimated Price', '${AppConstants.currencySymbol}${_order!.estimatedPrice?.toStringAsFixed(0) ?? '0'}'),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 1.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 35.w,
            child: Text(
              label,
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
