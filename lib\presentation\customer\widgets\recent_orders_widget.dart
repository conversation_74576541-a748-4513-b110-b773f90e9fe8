import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../../core/app_export.dart';
import '../../../core/services/order_service.dart';

class RecentOrdersWidget extends StatefulWidget {
  final List<OrderModel>? orders;
  final Function(OrderModel)? onOrderTap;

  const RecentOrdersWidget({
    Key? key,
    this.orders,
    this.onOrderTap,
  }) : super(key: key);

  @override
  State<RecentOrdersWidget> createState() => _RecentOrdersWidgetState();
}

class _RecentOrdersWidgetState extends State<RecentOrdersWidget> {
  List<OrderModel> _orders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    try {
      final orderService = OrderService.instance;
      final orders = await orderService.getOrders();

      if (mounted) {
        setState(() {
          _orders = orders;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _orders = _getMockOrders(); // Fallback to mock data
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(
            AppTheme.lightTheme.primaryColor,
          ),
        ),
      );
    }

    final orderList = widget.orders ?? _orders;

    if (orderList.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: orderList.length > 3 ? 3 : orderList.length, // Show max 3 recent orders
      separatorBuilder: (context, index) => SizedBox(height: 2.h),
      itemBuilder: (context, index) {
        final order = orderList[index];
        return _buildOrderCard(context, order);
      },
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.history,
            size: 48,
            color: Colors.grey[400],
          ),
          SizedBox(height: 2.h),
          Text(
            'No recent orders',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Your order history will appear here',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  List<OrderModel> _getMockOrders() {
    return [
      OrderModel(
        id: 'ORD001',
        customerId: 'CUST001',
        serviceType: AppConstants.serviceTypeTowing,
        status: AppConstants.orderStatusCompleted,
        description: 'Car breakdown on highway',
        pickupLocation: LocationInfo(
          latitude: 13.7563,
          longitude: 100.5018,
          address: 'Sukhumvit Road, Bangkok',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        dropoffLocation: LocationInfo(
          latitude: 13.7308,
          longitude: 100.5418,
          address: 'Garage on Rama IV',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        completedAt: DateTime.now().subtract(const Duration(days: 2, hours: 1)),
        finalPrice: 1500.0,
        vehicleInfo: VehicleInfo(
          make: 'Toyota',
          model: 'Camry',
          year: '2020',
          color: 'White',
          licensePlate: 'ABC-1234',
        ),
      ),
      OrderModel(
        id: 'ORD002',
        customerId: 'CUST001',
        serviceType: AppConstants.serviceTypeJumpStart,
        status: AppConstants.orderStatusCompleted,
        description: 'Dead battery in parking lot',
        pickupLocation: LocationInfo(
          latitude: 13.7460,
          longitude: 100.5392,
          address: 'Central World, Bangkok',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        completedAt: DateTime.now().subtract(const Duration(days: 5, hours: 1)),
        finalPrice: 800.0,
        vehicleInfo: VehicleInfo(
          make: 'Honda',
          model: 'Civic',
          year: '2019',
          color: 'Black',
          licensePlate: 'XYZ-5678',
        ),
      ),
      OrderModel(
        id: 'ORD003',
        customerId: 'CUST001',
        serviceType: AppConstants.serviceTypeFlatTire,
        status: AppConstants.orderStatusInProgress,
        description: 'Flat tire need replacement',
        pickupLocation: LocationInfo(
          latitude: 13.7997,
          longitude: 100.5533,
          address: 'Chatuchak Market, Bangkok',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        dropoffLocation: LocationInfo(
          latitude: 13.8200,
          longitude: 100.5500,
          address: 'Tire Shop on Phahonyothin',
          city: 'Bangkok',
          state: 'Bangkok',
        ),
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        estimatedPrice: 600.0,
        vehicleInfo: VehicleInfo(
          make: 'Mazda',
          model: '3',
          year: '2021',
          color: 'Red',
          licensePlate: 'DEF-9012',
        ),
      ),
    ];
  }

  Widget _buildOrderCard(BuildContext context, OrderModel order) {
    return GestureDetector(
      onTap: () {
        if (widget.onOrderTap != null) {
          widget.onOrderTap!(order);
        }
      },
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: _getStatusColor(order.status).withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: _getServiceColor(order.serviceType).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CustomIconWidget(
                        iconName: _getServiceIcon(order.serviceType),
                        color: _getServiceColor(order.serviceType),
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order.serviceType,
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'Order #${order.id.length > 8 ? order.id.substring(0, 8) : order.id}',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                _buildStatusChip(order.status),
              ],
            ),

            SizedBox(height: 2.h),

            // Vehicle info
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'directions_car',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Text(
                  _getVehicleDisplayText(order.vehicleInfo),
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            SizedBox(height: 1.h),

            // Location info
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'location_on',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'จุดรับ: ${order.pickupLocation.latitude.toStringAsFixed(6)}, ${order.pickupLocation.longitude.toStringAsFixed(6)}',
                    style: AppTheme.lightTheme.textTheme.bodyMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            SizedBox(height: 2.h),

            // Footer row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  DateFormat('MMM dd, yyyy • HH:mm').format(order.createdAt),
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  ),
                ),
                if (order.finalPrice != null || order.estimatedPrice != null)
                  Text(
                    '${AppConstants.currencySymbol}${(order.finalPrice ?? order.estimatedPrice)?.toStringAsFixed(0)}',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      color: AppTheme.lightTheme.primaryColor,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
              ],
            ),

            // Action buttons for active orders
            if (order.isInProgress || order.isAccepted) ...[
              SizedBox(height: 2.h),
              Row(
                children: [
                  if (order.isAccepted || order.isInProgress)
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            AppRoutes.trackServiceScreen,
                            arguments: {'orderId': order.id},
                          );
                        },
                        icon: CustomIconWidget(
                          iconName: 'my_location',
                          color: AppTheme.lightTheme.primaryColor,
                          size: 16,
                        ),
                        label: Text(
                          'Track',
                          style: TextStyle(
                            color: AppTheme.lightTheme.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: AppTheme.lightTheme.primaryColor),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  if (order.canBeCancelled) ...[
                    if (order.isAccepted || order.isInProgress) SizedBox(width: 2.w),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          _showCancelDialog(context, order);
                        },
                        icon: CustomIconWidget(
                          iconName: 'cancel',
                          color: Colors.red,
                          size: 16,
                        ),
                        label: Text(
                          'Cancel',
                          style: TextStyle(
                            color: Colors.red,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: Colors.red),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],

            // Rate button for completed orders
            if (order.canBeRated) ...[
              SizedBox(height: 2.h),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.ratingScreen,
                      arguments: {'orderId': order.id},
                    );
                  },
                  icon: CustomIconWidget(
                    iconName: 'star',
                    color: Colors.white,
                    size: 16,
                  ),
                  label: Text(
                    'Rate Service',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.lightTheme.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 3.w,
        vertical: 1.h,
      ),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: _getStatusColor(status),
          fontSize: 10.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return Colors.orange;
      case AppConstants.orderStatusAccepted:
        return Colors.blue;
      case AppConstants.orderStatusInProgress:
        return Colors.purple;
      case AppConstants.orderStatusCompleted:
        return Colors.green;
      case AppConstants.orderStatusCancelled:
        return Colors.red;
      case AppConstants.orderStatusRejected:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  Color _getServiceColor(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return Colors.red;
      case AppConstants.serviceTypeJumpStart:
        return Colors.orange;
      case AppConstants.serviceTypeFlatTire:
        return Colors.blue;
      case AppConstants.serviceTypeFuelDelivery:
        return Colors.green;
      case AppConstants.serviceTypeLockout:
        return Colors.purple;
      case AppConstants.serviceTypeWinchOut:
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }

  String _getServiceIcon(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return 'local_shipping';
      case AppConstants.serviceTypeJumpStart:
        return 'battery_charging_full';
      case AppConstants.serviceTypeFlatTire:
        return 'tire_repair';
      case AppConstants.serviceTypeFuelDelivery:
        return 'local_gas_station';
      case AppConstants.serviceTypeLockout:
        return 'lock_open';
      case AppConstants.serviceTypeWinchOut:
        return 'construction';
      default:
        return 'build';
    }
  }

  void _showCancelDialog(BuildContext context, OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: const Text('Are you sure you want to cancel this order?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement cancel order functionality
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  String _getVehicleDisplayText(VehicleInfo vehicleInfo) {
    List<String> parts = [];

    // Add make if not default
    if (vehicleInfo.make.isNotEmpty && vehicleInfo.make != 'ไม่ระบุ') {
      parts.add(vehicleInfo.make);
    }

    // Add model if not default and not empty
    if (vehicleInfo.model.isNotEmpty && vehicleInfo.model != 'ไม่ระบุ') {
      parts.add(vehicleInfo.model);
    }

    // Add license plate if not default
    if (vehicleInfo.licensePlate.isNotEmpty && vehicleInfo.licensePlate != 'ไม่ระบุ') {
      parts.add('(${vehicleInfo.licensePlate})');
    }

    return parts.isNotEmpty ? parts.join(' ') : 'ข้อมูลรถยนต์';
  }
}
