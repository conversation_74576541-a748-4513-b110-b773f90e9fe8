import 'package:flutter/material.dart';

import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import 'widgets/customer_register_form.dart';
import 'widgets/supplier_register_form.dart';
import 'widgets/agent_register_form.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen>
    with TickerProviderStateMixin {
  
  String _selectedRole = AppConstants.userRoleCustomer;
  bool _showForm = false;

  final List<Map<String, dynamic>> _userRoles = [
    {
      'role': AppConstants.userRoleCustomer,
      'title': 'Customer',
      'subtitle': 'Request towing services',
      'icon': 'person',
      'color': Colors.blue,
    },
    {
      'role': AppConstants.userRoleSupplier,
      'title': 'Supplier',
      'subtitle': 'Provide towing services',
      'icon': 'local_shipping',
      'color': Colors.green,
    },
    {
      'role': AppConstants.userRoleAgent,
      'title': 'Agent',
      'subtitle': 'Connect customers with suppliers',
      'icon': 'business_center',
      'color': Colors.orange,
    },
  ];

  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  void _handleRoleSelection(String role) {
    setState(() {
      _selectedRole = role;
      _showForm = true;
    });
  }

  void _handleBackToRoleSelection() {
    setState(() {
      _showForm = false;
    });
  }

  void _navigateToLogin() {
    Navigator.pushReplacementNamed(context, AppRoutes.loginScreen);
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: _showForm ? _buildRegistrationForm() : _buildRoleSelection(),
        ),
      ),
    );
  }

  Widget _buildRoleSelection() {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 6.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 4.h),

          // Back button
          Align(
            alignment: Alignment.centerLeft,
            child: IconButton(
              onPressed: _navigateToLogin,
              icon: CustomIconWidget(
                iconName: 'arrow_back_ios',
                color: AppTheme.lightTheme.colorScheme.onSurface,
                size: 24,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ),

          SizedBox(height: 4.h),

          // Logo
          Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.lightTheme.primaryColor,
                  AppTheme.lightTheme.primaryColor.withValues(alpha: 0.7),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: CustomIconWidget(
                iconName: 'local_shipping',
                color: Colors.white,
                size: 32,
              ),
            ),
          ),

          SizedBox(height: 3.h),

          // Title
          Text(
            'Join TowTruck Pro',
            style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 1.h),

          Text(
            'Choose your account type to get started',
            style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 4.h),

          // Role selection cards
          Column(
            children: _userRoles.map((role) {
              return Container(
                margin: EdgeInsets.only(bottom: 3.h),
                child: GestureDetector(
                  onTap: () => _handleRoleSelection(role['role']),
                  child: Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      color: AppTheme.lightTheme.cardColor,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: role['color'].withValues(alpha: 0.3),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: role['color'].withValues(alpha: 0.1),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 15.w,
                          height: 15.w,
                          decoration: BoxDecoration(
                            color: role['color'].withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Center(
                            child: CustomIconWidget(
                              iconName: role['icon'],
                              color: role['color'],
                              size: 28,
                            ),
                          ),
                        ),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                role['title'],
                                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  color: role['color'],
                                ),
                              ),
                              SizedBox(height: 0.5.h),
                              Text(
                                role['subtitle'],
                                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                        CustomIconWidget(
                          iconName: 'arrow_forward_ios',
                          color: role['color'],
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          SizedBox(height: 2.h),

          // Login link
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Already have an account? ',
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                ),
              ),
              TextButton(
                onPressed: _navigateToLogin,
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  'Sign In',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.lightTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 4.h),
        ],
      ),
    );
  }

  Widget _buildRegistrationForm() {
    switch (_selectedRole) {
      case AppConstants.userRoleCustomer:
        return CustomerRegisterForm(onBack: _handleBackToRoleSelection);
      case AppConstants.userRoleSupplier:
        return SupplierRegisterForm(onBack: _handleBackToRoleSelection);
      case AppConstants.userRoleAgent:
        return AgentRegisterForm(onBack: _handleBackToRoleSelection);
      default:
        return CustomerRegisterForm(onBack: _handleBackToRoleSelection);
    }
  }
}
