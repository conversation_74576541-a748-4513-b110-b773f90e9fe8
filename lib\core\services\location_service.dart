import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:towtruck_pro/core/constants/app_constants.dart';

/// Location service for handling GPS and location-related operations
class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  Position? _currentPosition;
  bool _isLocationServiceEnabled = false;
  LocationPermission _locationPermission = LocationPermission.denied;

  /// Get current position
  Position? get currentPosition => _currentPosition;

  /// Check if location service is enabled
  bool get isLocationServiceEnabled => _isLocationServiceEnabled;

  /// Get location permission status
  LocationPermission get locationPermission => _locationPermission;

  /// Initialize location service
  Future<bool> initialize() async {
    try {
      // Check if location services are enabled
      _isLocationServiceEnabled = await Geolocator.isLocationServiceEnabled();
      
      if (!_isLocationServiceEnabled) {
        return false;
      }

      // Check location permission
      _locationPermission = await Geolocator.checkPermission();
      
      if (_locationPermission == LocationPermission.denied) {
        _locationPermission = await Geolocator.requestPermission();
      }

      if (_locationPermission == LocationPermission.deniedForever) {
        return false;
      }

      // Get current position
      await getCurrentPosition();
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Request location permission
  Future<bool> requestLocationPermission() async {
    try {
      final permission = await Permission.location.request();
      
      if (permission.isGranted) {
        _locationPermission = LocationPermission.whileInUse;
        return true;
      } else if (permission.isPermanentlyDenied) {
        _locationPermission = LocationPermission.deniedForever;
        return false;
      } else {
        _locationPermission = LocationPermission.denied;
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// Get current position
  Future<Position?> getCurrentPosition({bool forceRefresh = false}) async {
    try {
      if (!_isLocationServiceEnabled) {
        throw LocationServiceDisabledException();
      }

      if (_locationPermission == LocationPermission.denied ||
          _locationPermission == LocationPermission.deniedForever) {
        throw LocationPermissionDeniedException();
      }

      if (_currentPosition != null && !forceRefresh) {
        // Check if cached position is still valid (less than 5 minutes old)
        final now = DateTime.now();
        final positionTime = DateTime.fromMillisecondsSinceEpoch(
          _currentPosition!.timestamp.millisecondsSinceEpoch,
        );
        final difference = now.difference(positionTime);
        
        if (difference.inMinutes < 5) {
          return _currentPosition;
        }
      }

      _currentPosition = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
          timeLimit: Duration(seconds: 10),
        ),
      );

      return _currentPosition;
    } catch (e) {
      rethrow;
    }
  }

  /// Get last known position
  Future<Position?> getLastKnownPosition() async {
    try {
      return await Geolocator.getLastKnownPosition();
    } catch (e) {
      return null;
    }
  }

  /// Calculate distance between two points
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Calculate bearing between two points
  double calculateBearing(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Get address from coordinates (reverse geocoding)
  Future<String?> getAddressFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        return _formatAddress(placemark);
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get coordinates from address (geocoding)
  Future<Position?> getCoordinatesFromAddress(String address) async {
    try {
      final locations = await locationFromAddress(address);
      
      if (locations.isNotEmpty) {
        final location = locations.first;
        return Position(
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Format placemark to readable address
  String _formatAddress(Placemark placemark) {
    final components = <String>[];
    
    if (placemark.street?.isNotEmpty == true) {
      components.add(placemark.street!);
    }
    
    if (placemark.subLocality?.isNotEmpty == true) {
      components.add(placemark.subLocality!);
    }
    
    if (placemark.locality?.isNotEmpty == true) {
      components.add(placemark.locality!);
    }
    
    if (placemark.administrativeArea?.isNotEmpty == true) {
      components.add(placemark.administrativeArea!);
    }
    
    if (placemark.postalCode?.isNotEmpty == true) {
      components.add(placemark.postalCode!);
    }
    
    if (placemark.country?.isNotEmpty == true) {
      components.add(placemark.country!);
    }
    
    return components.join(', ');
  }

  /// Check if location is within service area
  bool isWithinServiceArea(
    double latitude,
    double longitude,
    double centerLatitude,
    double centerLongitude,
    double radiusInKm,
  ) {
    final distance = calculateDistance(
      latitude,
      longitude,
      centerLatitude,
      centerLongitude,
    );
    
    return distance <= (radiusInKm * 1000); // Convert km to meters
  }

  /// Get nearby suppliers within radius
  List<T> getNearbyItems<T>(
    List<T> items,
    double userLatitude,
    double userLongitude,
    double radiusInKm,
    double Function(T) getLatitude,
    double Function(T) getLongitude,
  ) {
    return items.where((item) {
      final itemLatitude = getLatitude(item);
      final itemLongitude = getLongitude(item);
      
      return isWithinServiceArea(
        itemLatitude,
        itemLongitude,
        userLatitude,
        userLongitude,
        radiusInKm,
      );
    }).toList();
  }

  /// Sort items by distance
  List<T> sortByDistance<T>(
    List<T> items,
    double userLatitude,
    double userLongitude,
    double Function(T) getLatitude,
    double Function(T) getLongitude,
  ) {
    final itemsWithDistance = items.map((item) {
      final distance = calculateDistance(
        userLatitude,
        userLongitude,
        getLatitude(item),
        getLongitude(item),
      );
      return MapEntry(item, distance);
    }).toList();

    itemsWithDistance.sort((a, b) => a.value.compareTo(b.value));
    
    return itemsWithDistance.map((entry) => entry.key).toList();
  }

  /// Start location tracking
  Stream<Position> startLocationTracking({
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 10,
  }) {
    const locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    );

    return Geolocator.getPositionStream(locationSettings: locationSettings);
  }

  /// Open location settings
  Future<bool> openLocationSettings() async {
    return await Geolocator.openLocationSettings();
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    return await Geolocator.openAppSettings();
  }

  /// Check if coordinates are valid
  bool isValidCoordinates(double latitude, double longitude) {
    return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;
  }

  /// Get default location (Bangkok, Thailand)
  Position getDefaultLocation() {
    return Position(
      latitude: AppConstants.defaultLatitude,
      longitude: AppConstants.defaultLongitude,
      timestamp: DateTime.now(),
      accuracy: 0,
      altitude: 0,
      heading: 0,
      speed: 0,
      speedAccuracy: 0,
      altitudeAccuracy: 0,
      headingAccuracy: 0,
    );
  }

  /// Format distance for display
  String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()} m';
    } else {
      final km = distanceInMeters / 1000;
      return '${km.toStringAsFixed(1)} km';
    }
  }

  /// Format duration for display
  String formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes.remainder(60)}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }
}

/// Custom exceptions
class LocationServiceDisabledException implements Exception {
  final String message = 'Location services are disabled';
  
  @override
  String toString() => message;
}

class LocationPermissionDeniedException implements Exception {
  final String message = 'Location permission denied';
  
  @override
  String toString() => message;
}
