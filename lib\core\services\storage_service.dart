import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// Storage service for handling local data persistence
class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  SharedPreferences? _prefs;

  /// Initialize storage service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Get SharedPreferences instance
  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call initialize() first.');
    }
    return _prefs!;
  }

  /// Store string value
  Future<bool> setString(String key, String value) async {
    return await prefs.setString(key, value);
  }

  /// Get string value
  Future<String?> getString(String key) async {
    return prefs.getString(key);
  }

  /// Store integer value
  Future<bool> setInt(String key, int value) async {
    return await prefs.setInt(key, value);
  }

  /// Get integer value
  Future<int?> getInt(String key) async {
    return prefs.getInt(key);
  }

  /// Store double value
  Future<bool> setDouble(String key, double value) async {
    return await prefs.setDouble(key, value);
  }

  /// Get double value
  Future<double?> getDouble(String key) async {
    return prefs.getDouble(key);
  }

  /// Store boolean value
  Future<bool> setBool(String key, bool value) async {
    return await prefs.setBool(key, value);
  }

  /// Get boolean value
  Future<bool?> getBool(String key) async {
    return prefs.getBool(key);
  }

  /// Store list of strings
  Future<bool> setStringList(String key, List<String> value) async {
    return await prefs.setStringList(key, value);
  }

  /// Get list of strings
  Future<List<String>?> getStringList(String key) async {
    return prefs.getStringList(key);
  }

  /// Store object as JSON string
  Future<bool> setObject(String key, Map<String, dynamic> value) async {
    final jsonString = json.encode(value);
    return await setString(key, jsonString);
  }

  /// Get object from JSON string
  Future<Map<String, dynamic>?> getObject(String key) async {
    final jsonString = await getString(key);
    if (jsonString == null) return null;
    
    try {
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// Store list of objects as JSON string
  Future<bool> setObjectList(String key, List<Map<String, dynamic>> value) async {
    final jsonString = json.encode(value);
    return await setString(key, jsonString);
  }

  /// Get list of objects from JSON string
  Future<List<Map<String, dynamic>>?> getObjectList(String key) async {
    final jsonString = await getString(key);
    if (jsonString == null) return null;
    
    try {
      final List<dynamic> decoded = json.decode(jsonString);
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      return null;
    }
  }

  /// Remove value by key
  Future<bool> remove(String key) async {
    return await prefs.remove(key);
  }

  /// Check if key exists
  Future<bool> containsKey(String key) async {
    return prefs.containsKey(key);
  }

  /// Clear all stored data
  Future<bool> clear() async {
    return await prefs.clear();
  }

  /// Get all keys
  Future<Set<String>> getKeys() async {
    return prefs.getKeys();
  }

  /// Get all values as Map
  Future<Map<String, dynamic>> getAll() async {
    final keys = await getKeys();
    final Map<String, dynamic> result = {};
    
    for (final key in keys) {
      final value = prefs.get(key);
      if (value != null) {
        result[key] = value;
      }
    }
    
    return result;
  }

  /// Reload preferences from disk
  Future<void> reload() async {
    await prefs.reload();
  }

  /// Store encrypted string (basic implementation)
  Future<bool> setSecureString(String key, String value) async {
    // In a real app, you would use a proper encryption library
    // For now, we'll just use base64 encoding as a placeholder
    final encoded = base64.encode(utf8.encode(value));
    return await setString('secure_$key', encoded);
  }

  /// Get encrypted string (basic implementation)
  Future<String?> getSecureString(String key) async {
    final encoded = await getString('secure_$key');
    if (encoded == null) return null;
    
    try {
      final decoded = utf8.decode(base64.decode(encoded));
      return decoded;
    } catch (e) {
      return null;
    }
  }

  /// Remove encrypted string
  Future<bool> removeSecureString(String key) async {
    return await remove('secure_$key');
  }

  /// Batch operations
  Future<void> setBatch(Map<String, dynamic> data) async {
    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;
      
      if (value is String) {
        await setString(key, value);
      } else if (value is int) {
        await setInt(key, value);
      } else if (value is double) {
        await setDouble(key, value);
      } else if (value is bool) {
        await setBool(key, value);
      } else if (value is List<String>) {
        await setStringList(key, value);
      } else if (value is Map<String, dynamic>) {
        await setObject(key, value);
      } else if (value is List<Map<String, dynamic>>) {
        await setObjectList(key, value);
      }
    }
  }

  /// Get batch data
  Future<Map<String, dynamic>> getBatch(List<String> keys) async {
    final Map<String, dynamic> result = {};
    
    for (final key in keys) {
      final value = prefs.get(key);
      if (value != null) {
        result[key] = value;
      }
    }
    
    return result;
  }

  /// Remove batch data
  Future<void> removeBatch(List<String> keys) async {
    for (final key in keys) {
      await remove(key);
    }
  }

  /// Check storage size (approximate)
  Future<int> getStorageSize() async {
    final allData = await getAll();
    final jsonString = json.encode(allData);
    return utf8.encode(jsonString).length;
  }

  /// Export all data
  Future<Map<String, dynamic>> exportData() async {
    return await getAll();
  }

  /// Import data
  Future<void> importData(Map<String, dynamic> data) async {
    await clear();
    await setBatch(data);
  }

  /// Backup data to JSON string
  Future<String> backupToJson() async {
    final allData = await getAll();
    return json.encode(allData);
  }

  /// Restore data from JSON string
  Future<void> restoreFromJson(String jsonString) async {
    try {
      final Map<String, dynamic> data = json.decode(jsonString);
      await importData(data);
    } catch (e) {
      throw Exception('Invalid backup data format');
    }
  }
}
