import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class StatusToggleWidget extends StatelessWidget {
  final String currentStatus;
  final Function(String) onStatusChanged;

  const StatusToggleWidget({
    Key? key,
    required this.currentStatus,
    required this.onStatusChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomIconWidget(
                iconName: 'power_settings_new',
                color: Colors.white,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'สถานะการให้บริการ',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 2.h),
          
          Row(
            children: [
              Expanded(
                child: _buildStatusOption(
                  status: AppConstants.supplierStatusOnline,
                  label: 'ออนไลน์',
                  description: 'พร้อมรับงาน',
                  icon: 'check_circle',
                  color: Colors.green,
                  isSelected: currentStatus == AppConstants.supplierStatusOnline,
                ),
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: _buildStatusOption(
                  status: AppConstants.supplierStatusOffline,
                  label: 'ออฟไลน์',
                  description: 'ไม่พร้อมรับงาน',
                  icon: 'cancel',
                  color: Colors.grey,
                  isSelected: currentStatus == AppConstants.supplierStatusOffline,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusOption({
    required String status,
    required String label,
    required String description,
    required String icon,
    required Color color,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () => onStatusChanged(status),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: isSelected 
              ? Colors.white.withValues(alpha: 0.2)
              : Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected 
                ? Colors.white.withValues(alpha: 0.4)
                : Colors.white.withValues(alpha: 0.1),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 10.w,
              height: 10.w,
              decoration: BoxDecoration(
                color: isSelected 
                    ? color.withValues(alpha: 0.2)
                    : Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: icon,
                  color: isSelected ? color : Colors.white.withValues(alpha: 0.7),
                  size: 20,
                ),
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.7),
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              description,
              style: TextStyle(
                color: isSelected ? Colors.white.withValues(alpha: 0.9) : Colors.white.withValues(alpha: 0.5),
                fontSize: 9.sp,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
