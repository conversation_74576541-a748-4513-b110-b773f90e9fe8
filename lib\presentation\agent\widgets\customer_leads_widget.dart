import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';


import '../../../core/app_export.dart';

class CustomerLeadsWidget extends StatelessWidget {
  final List<Map<String, dynamic>> leads;
  final Function(Map<String, dynamic>) onContactCustomer;
  final Function(Map<String, dynamic>) onCreateOrder;

  const CustomerLeadsWidget({
    Key? key,
    required this.leads,
    required this.onContactCustomer,
    required this.onCreateOrder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: leads.length,
      separatorBuilder: (context, index) => SizedBox(height: 2.h),
      itemBuilder: (context, index) {
        final lead = leads[index];
        return _buildLeadCard(context, lead);
      },
    );
  }

  Widget _buildLeadCard(BuildContext context, Map<String, dynamic> lead) {
    final timeAgo = DateTime.now().difference(lead['createdAt'] as DateTime);
    final urgencyColor = _getUrgencyColor(lead['urgency']);
    
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: urgencyColor.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: urgencyColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: _getServiceColor(lead['serviceNeeded']).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: CustomIconWidget(
                      iconName: _getServiceIcon(lead['serviceNeeded']),
                      color: _getServiceColor(lead['serviceNeeded']),
                      size: 20,
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        lead['name'],
                        style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        lead['serviceNeeded'],
                        style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 2.w,
                      vertical: 0.5.h,
                    ),
                    decoration: BoxDecoration(
                      color: urgencyColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CustomIconWidget(
                          iconName: _getUrgencyIcon(lead['urgency']),
                          color: urgencyColor,
                          size: 12,
                        ),
                        SizedBox(width: 1.w),
                        Text(
                          lead['urgency'].toUpperCase(),
                          style: TextStyle(
                            color: urgencyColor,
                            fontSize: 9.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    _formatTimeAgo(timeAgo),
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Contact info
          Row(
            children: [
              CustomIconWidget(
                iconName: 'phone',
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                size: 16,
              ),
              SizedBox(width: 2.w),
              Text(
                lead['phone'],
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          SizedBox(height: 1.h),

          // Location with distance
          Row(
            children: [
              CustomIconWidget(
                iconName: 'location_on',
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                size: 16,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      lead['location'],
                      style: AppTheme.lightTheme.textTheme.bodyMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '~${lead['distance'].toStringAsFixed(1)} km away',
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.lightTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Value and commission info
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Estimated Value',
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: Colors.green.shade700,
                      ),
                    ),
                    Text(
                      '${AppConstants.currencySymbol}${lead['estimatedValue'].toStringAsFixed(0)}',
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Your Commission (10%)',
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: Colors.purple.shade700,
                      ),
                    ),
                    Text(
                      '${AppConstants.currencySymbol}${(lead['estimatedValue'] * 0.10).toStringAsFixed(0)}',
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        color: Colors.purple.shade700,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: 2.h),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => onContactCustomer(lead),
                  icon: CustomIconWidget(
                    iconName: 'phone',
                    color: AppTheme.lightTheme.primaryColor,
                    size: 16,
                  ),
                  label: Text(
                    'Call Customer',
                    style: TextStyle(
                      color: AppTheme.lightTheme.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: AppTheme.lightTheme.primaryColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                flex: 2,
                child: ElevatedButton.icon(
                  onPressed: () => onCreateOrder(lead),
                  icon: CustomIconWidget(
                    iconName: 'add',
                    color: Colors.white,
                    size: 16,
                  ),
                  label: Text(
                    'Create Order',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.lightTheme.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),

          // Hot lead indicator
          if (lead['urgency'] == 'High' && timeAgo.inMinutes < 30) ...[
            SizedBox(height: 1.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 1.h),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomIconWidget(
                    iconName: 'local_fire_department',
                    color: Colors.red,
                    size: 16,
                  ),
                  SizedBox(width: 1.w),
                  Text(
                    'Hot Lead - Contact immediately for best results!',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Lead score indicator
          SizedBox(height: 1.h),
          Row(
            children: [
              Text(
                'Lead Score: ',
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                ),
              ),
              ...List.generate(5, (index) {
                final score = _calculateLeadScore(lead);
                return CustomIconWidget(
                  iconName: index < score ? 'star' : 'star_border',
                  color: index < score ? Colors.amber : Colors.grey,
                  size: 14,
                );
              }),
              SizedBox(width: 2.w),
              Text(
                _getLeadScoreText(_calculateLeadScore(lead)),
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: _getLeadScoreColor(_calculateLeadScore(lead)),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getUrgencyColor(String urgency) {
    switch (urgency.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getUrgencyIcon(String urgency) {
    switch (urgency.toLowerCase()) {
      case 'high':
        return 'priority_high';
      case 'medium':
        return 'remove';
      case 'low':
        return 'keyboard_arrow_down';
      default:
        return 'help';
    }
  }

  Color _getServiceColor(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return Colors.red;
      case AppConstants.serviceTypeJumpStart:
        return Colors.orange;
      case AppConstants.serviceTypeFlatTire:
        return Colors.blue;
      case AppConstants.serviceTypeFuelDelivery:
        return Colors.green;
      case AppConstants.serviceTypeLockout:
        return Colors.purple;
      case AppConstants.serviceTypeWinchOut:
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }

  String _getServiceIcon(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return 'local_shipping';
      case AppConstants.serviceTypeJumpStart:
        return 'battery_charging_full';
      case AppConstants.serviceTypeFlatTire:
        return 'tire_repair';
      case AppConstants.serviceTypeFuelDelivery:
        return 'local_gas_station';
      case AppConstants.serviceTypeLockout:
        return 'lock_open';
      case AppConstants.serviceTypeWinchOut:
        return 'construction';
      default:
        return 'build';
    }
  }

  String _formatTimeAgo(Duration duration) {
    if (duration.inMinutes < 1) {
      return 'Just now';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ago';
    } else if (duration.inHours < 24) {
      return '${duration.inHours}h ago';
    } else {
      return '${duration.inDays}d ago';
    }
  }

  int _calculateLeadScore(Map<String, dynamic> lead) {
    int score = 0;
    
    // Urgency factor
    switch (lead['urgency'].toLowerCase()) {
      case 'high':
        score += 2;
        break;
      case 'medium':
        score += 1;
        break;
    }
    
    // Distance factor (closer = better)
    if (lead['distance'] < 2.0) {
      score += 2;
    } else if (lead['distance'] < 5.0) {
      score += 1;
    }
    
    // Value factor
    if (lead['estimatedValue'] > 1000) {
      score += 1;
    }
    
    // Time factor (newer = better)
    final timeAgo = DateTime.now().difference(lead['createdAt'] as DateTime);
    if (timeAgo.inMinutes < 30) {
      score += 1;
    }
    
    return score.clamp(1, 5);
  }

  String _getLeadScoreText(int score) {
    switch (score) {
      case 5:
        return 'Excellent';
      case 4:
        return 'Very Good';
      case 3:
        return 'Good';
      case 2:
        return 'Fair';
      case 1:
      default:
        return 'Poor';
    }
  }

  Color _getLeadScoreColor(int score) {
    switch (score) {
      case 5:
        return Colors.green;
      case 4:
        return Colors.lightGreen;
      case 3:
        return Colors.orange;
      case 2:
        return Colors.deepOrange;
      case 1:
      default:
        return Colors.red;
    }
  }
}
