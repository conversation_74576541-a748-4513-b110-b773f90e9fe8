import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../../core/app_export.dart';

class ActiveJobsWidget extends StatelessWidget {
  final List<OrderModel> jobs;
  final Function(String) onJobTap;

  const ActiveJobsWidget({
    Key? key,
    required this.jobs,
    required this.onJobTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: jobs.length,
      separatorBuilder: (context, index) => SizedBox(height: 2.h),
      itemBuilder: (context, index) {
        final job = jobs[index];
        return _buildJobCard(context, job);
      },
    );
  }

  Widget _buildJobCard(BuildContext context, OrderModel job) {
    final duration = DateTime.now().difference(job.acceptedAt ?? job.createdAt);
    
    return GestureDetector(
      onTap: () => onJobTap(job.id),
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: _getServiceColor(job.serviceType).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CustomIconWidget(
                        iconName: _getServiceIcon(job.serviceType),
                        color: _getServiceColor(job.serviceType),
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          job.serviceType,
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'Job #${job.id.substring(0, 8)}',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 2.w,
                        vertical: 0.5.h,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'IN PROGRESS',
                        style: TextStyle(
                          color: Colors.orange,
                          fontSize: 9.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      _formatDuration(duration),
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            SizedBox(height: 2.h),

            // Customer and vehicle info
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'person',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Customer #${job.customerId.substring(0, 8)}',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            SizedBox(height: 1.h),

            Row(
              children: [
                CustomIconWidget(
                  iconName: 'directions_car',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Text(
                  '${job.vehicleInfo.make} ${job.vehicleInfo.model} (${job.vehicleInfo.licensePlate})',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            SizedBox(height: 1.h),

            // Location
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'location_on',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    job.pickupLocation.address,
                    style: AppTheme.lightTheme.textTheme.bodyMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            if (job.description.isNotEmpty) ...[
              SizedBox(height: 1.h),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomIconWidget(
                    iconName: 'description',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 16,
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Text(
                      job.description,
                      style: AppTheme.lightTheme.textTheme.bodyMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],

            SizedBox(height: 2.h),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // TODO: Open navigation app
                      _showNavigationOptions(context, job);
                    },
                    icon: CustomIconWidget(
                      iconName: 'navigation',
                      color: AppTheme.lightTheme.primaryColor,
                      size: 16,
                    ),
                    label: Text(
                      'Navigate',
                      style: TextStyle(
                        color: AppTheme.lightTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppTheme.lightTheme.primaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      _showUpdateStatusDialog(context, job);
                    },
                    icon: CustomIconWidget(
                      iconName: 'update',
                      color: Colors.white,
                      size: 16,
                    ),
                    label: Text(
                      'Update',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.lightTheme.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Price info
            if (job.estimatedPrice != null) ...[
              SizedBox(height: 2.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Estimated Earnings',
                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${AppConstants.currencySymbol}${job.estimatedPrice!.toStringAsFixed(0)}',
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getServiceColor(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return Colors.red;
      case AppConstants.serviceTypeJumpStart:
        return Colors.orange;
      case AppConstants.serviceTypeFlatTire:
        return Colors.blue;
      case AppConstants.serviceTypeFuelDelivery:
        return Colors.green;
      case AppConstants.serviceTypeLockout:
        return Colors.purple;
      case AppConstants.serviceTypeWinchOut:
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }

  String _getServiceIcon(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return 'local_shipping';
      case AppConstants.serviceTypeJumpStart:
        return 'battery_charging_full';
      case AppConstants.serviceTypeFlatTire:
        return 'tire_repair';
      case AppConstants.serviceTypeFuelDelivery:
        return 'local_gas_station';
      case AppConstants.serviceTypeLockout:
        return 'lock_open';
      case AppConstants.serviceTypeWinchOut:
        return 'construction';
      default:
        return 'build';
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes.remainder(60)}m ago';
    } else {
      return '${duration.inMinutes}m ago';
    }
  }

  void _showNavigationOptions(BuildContext context, OrderModel job) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Navigate to Customer',
              style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
              ),
            ),
            SizedBox(height: 2.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'map',
                color: Colors.blue,
                size: 24,
              ),
              title: const Text('Google Maps'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Open Google Maps
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'navigation',
                color: Colors.green,
                size: 24,
              ),
              title: const Text('Waze'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Open Waze
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showUpdateStatusDialog(BuildContext context, OrderModel job) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Job Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: CustomIconWidget(
                iconName: 'directions_car',
                color: Colors.blue,
                size: 24,
              ),
              title: const Text('Arrived at Location'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Update status to arrived
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'build',
                color: Colors.orange,
                size: 24,
              ),
              title: const Text('Started Service'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Update status to in progress
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'check_circle',
                color: Colors.green,
                size: 24,
              ),
              title: const Text('Job Completed'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Update status to completed
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
