import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

class DestinationPickerScreen extends StatefulWidget {
  final Position currentLocation;
  final LatLng? selectedDestination;

  const DestinationPickerScreen({
    Key? key,
    required this.currentLocation,
    this.selectedDestination,
  }) : super(key: key);

  @override
  State<DestinationPickerScreen> createState() => _DestinationPickerScreenState();
}

class _DestinationPickerScreenState extends State<DestinationPickerScreen> {
  GoogleMapController? _mapController;
  LatLng? _selectedDestination;
  final Set<Marker> _markers = {};

  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  List<Location> _searchResults = [];
  String? _selectedAddress;

  @override
  void initState() {
    super.initState();
    _selectedDestination = widget.selectedDestination;
    _initializeMarkers();
  }

  void _initializeMarkers() {
    // Add current location marker
    _markers.add(
      Marker(
        markerId: const MarkerId('current_location'),
        position: LatLng(widget.currentLocation.latitude, widget.currentLocation.longitude),
        infoWindow: const InfoWindow(
          title: 'Your Location',
          snippet: 'Starting point',
        ),
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
      ),
    );

    // Add destination marker if exists
    if (_selectedDestination != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('destination'),
          position: _selectedDestination!,
          infoWindow: const InfoWindow(
            title: 'Destination',
            snippet: 'Drop-off point',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      );
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  void _onMapTap(LatLng position) {
    setState(() {
      _selectedDestination = position;
      
      // Remove old destination marker
      _markers.removeWhere((marker) => marker.markerId.value == 'destination');
      
      // Add new destination marker
      _markers.add(
        Marker(
          markerId: const MarkerId('destination'),
          position: position,
          infoWindow: const InfoWindow(
            title: 'Destination',
            snippet: 'Drop-off point',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      );
    });
  }

  void _confirmDestination() {
    if (_selectedDestination != null) {
      Navigator.pop(context, _selectedDestination);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a destination on the map'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _clearDestination() {
    setState(() {
      _selectedDestination = null;
      _selectedAddress = null;
      _searchController.clear();
      _markers.removeWhere((marker) => marker.markerId.value == 'destination');
    });
  }

  Future<void> _searchLocation(String query) async {
    if (query.isEmpty) return;

    setState(() {
      _isSearching = true;
    });

    try {
      List<Location> locations = await locationFromAddress(query);
      setState(() {
        _searchResults = locations;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
        _searchResults = [];
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not find location: $query'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  void _selectSearchResult(Location location) async {
    final latLng = LatLng(location.latitude, location.longitude);

    // Get address from coordinates
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude
      );

      String address = '';
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        address = '${placemark.name ?? ''} ${placemark.street ?? ''} ${placemark.locality ?? ''} ${placemark.country ?? ''}'.trim();
      }

      setState(() {
        _selectedDestination = latLng;
        _selectedAddress = address.isNotEmpty ? address : 'Selected Location';
        _searchResults = [];
      });

      // Update marker
      _markers.removeWhere((marker) => marker.markerId.value == 'destination');
      _markers.add(
        Marker(
          markerId: const MarkerId('destination'),
          position: latLng,
          infoWindow: InfoWindow(
            title: 'Destination',
            snippet: _selectedAddress,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      );

      // Move camera to selected location
      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: latLng,
              zoom: 16.0,
            ),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _selectedDestination = latLng;
        _selectedAddress = 'Selected Location';
        _searchResults = [];
      });

      // Update marker
      _markers.removeWhere((marker) => marker.markerId.value == 'destination');
      _markers.add(
        Marker(
          markerId: const MarkerId('destination'),
          position: latLng,
          infoWindow: const InfoWindow(
            title: 'Destination',
            snippet: 'Selected Location',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Destination'),
        backgroundColor: AppTheme.lightTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_selectedDestination != null)
            IconButton(
              onPressed: _clearDestination,
              icon: const Icon(Icons.clear),
              tooltip: 'Clear destination',
            ),
        ],
      ),
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            onMapCreated: _onMapCreated,
            initialCameraPosition: CameraPosition(
              target: _selectedDestination ?? 
                     LatLng(widget.currentLocation.latitude, widget.currentLocation.longitude),
              zoom: 15.0,
            ),
            markers: _markers,
            onTap: _onMapTap,
            myLocationEnabled: false,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: true,
            mapToolbarEnabled: false,
            compassEnabled: true,
            trafficEnabled: false,
            buildingsEnabled: true,
            indoorViewEnabled: false,
            mapType: MapType.normal,
          ),

          // Search overlay
          Positioned(
            top: 2.h,
            left: 4.w,
            right: 4.w,
            child: Column(
              children: [
                // Search bar
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search for a place...',
                      prefixIcon: Icon(
                        Icons.search,
                        color: AppTheme.lightTheme.primaryColor,
                      ),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchResults = [];
                                });
                              },
                              icon: const Icon(Icons.clear),
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 4.w,
                        vertical: 2.h,
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {}); // Update suffixIcon
                      if (value.length > 2) {
                        _searchLocation(value);
                      } else {
                        setState(() {
                          _searchResults = [];
                        });
                      }
                    },
                    onSubmitted: _searchLocation,
                  ),
                ),

                // Search results
                if (_searchResults.isNotEmpty)
                  Container(
                    margin: EdgeInsets.only(top: 1.h),
                    constraints: BoxConstraints(maxHeight: 30.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ListView.separated(
                      shrinkWrap: true,
                      itemCount: _searchResults.length > 5 ? 5 : _searchResults.length,
                      separatorBuilder: (context, index) => const Divider(height: 1),
                      itemBuilder: (context, index) {
                        final location = _searchResults[index];
                        return ListTile(
                          leading: Icon(
                            Icons.location_on,
                            color: AppTheme.lightTheme.primaryColor,
                          ),
                          title: Text(
                            'Location ${index + 1}',
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          subtitle: Text(
                            'Lat: ${location.latitude.toStringAsFixed(4)}, Lng: ${location.longitude.toStringAsFixed(4)}',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          onTap: () => _selectSearchResult(location),
                        );
                      },
                    ),
                  ),

                // Loading indicator
                if (_isSearching)
                  Container(
                    margin: EdgeInsets.only(top: 1.h),
                    padding: EdgeInsets.all(3.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppTheme.lightTheme.primaryColor,
                            ),
                          ),
                        ),
                        SizedBox(width: 3.w),
                        const Text('Searching...'),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          // Destination info and confirm button
          if (_selectedDestination != null)
            Positioned(
              bottom: 4.h,
              left: 4.w,
              right: 4.w,
              child: Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          color: Colors.red,
                          size: 24,
                        ),
                        SizedBox(width: 3.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Selected Destination',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14.sp,
                                ),
                              ),
                              if (_selectedAddress != null) ...[
                                SizedBox(height: 0.5.h),
                                Text(
                                  _selectedAddress!,
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    color: Colors.grey[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                              SizedBox(height: 0.5.h),
                              Text(
                                'Lat: ${_selectedDestination!.latitude.toStringAsFixed(6)}',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: Colors.grey[600],
                                ),
                              ),
                              Text(
                                'Lng: ${_selectedDestination!.longitude.toStringAsFixed(6)}',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: 3.h),
                    
                    // Confirm button
                    SizedBox(
                      width: double.infinity,
                      height: 6.h,
                      child: ElevatedButton(
                        onPressed: _confirmDestination,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.check,
                              size: 24,
                            ),
                            SizedBox(width: 2.w),
                            Text(
                              'Confirm Destination',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _mapController?.dispose();
    super.dispose();
  }
}
