import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({Key? key}) : super(key: key);

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen>
    with TickerProviderStateMixin {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _emailController = TextEditingController();
  final FocusNode _emailFocusNode = FocusNode();

  bool _isLoading = false;
  String? _emailError;
  String? _generalError;
  bool _isEmailSent = false;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupFocusListener();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeController.forward();
    _slideController.forward();
  }

  void _setupFocusListener() {
    _emailFocusNode.addListener(() {
      if (_emailFocusNode.hasFocus) {
        _clearErrors();
      }
    });
  }

  void _clearErrors() {
    if (_emailError != null || _generalError != null) {
      setState(() {
        _emailError = null;
        _generalError = null;
      });
    }
  }

  bool _validateEmail() {
    if (_emailController.text.trim().isEmpty) {
      setState(() {
        _emailError = 'Email is required';
      });
      return false;
    } else if (!RegExp(AppConstants.emailPattern).hasMatch(_emailController.text.trim())) {
      setState(() {
        _emailError = 'Please enter a valid email address';
      });
      return false;
    }
    return true;
  }

  Future<void> _handleSendResetLink() async {
    if (!_validateEmail()) return;

    setState(() {
      _isLoading = true;
      _generalError = null;
    });

    try {
      // Simulate API call to send reset link
      await Future.delayed(const Duration(seconds: 2));

      // Success - trigger haptic feedback
      HapticFeedback.lightImpact();

      setState(() {
        _isEmailSent = true;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _generalError = 'Failed to send reset link. Please try again.';
        _isLoading = false;
      });
      HapticFeedback.heavyImpact();
    }
  }

  void _handleBackNavigation() {
    Navigator.pop(context);
  }

  void _handleResendEmail() {
    setState(() {
      _isEmailSent = false;
    });
    _handleSendResetLink();
  }

  void _handleBackToLogin() {
    Navigator.pushReplacementNamed(context, '/login-screen');
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _emailController.dispose();
    _emailFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: IntrinsicHeight(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 6.w),
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(height: 4.h),

                          // Back button
                          Align(
                            alignment: Alignment.centerLeft,
                            child: IconButton(
                              onPressed: _handleBackNavigation,
                              icon: CustomIconWidget(
                                iconName: 'arrow_back_ios',
                                color: AppTheme.lightTheme.colorScheme.onSurface,
                                size: 24,
                              ),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ),

                          SizedBox(height: 4.h),

                          // Icon
                          Container(
                            width: 25.w,
                            height: 25.w,
                            decoration: BoxDecoration(
                              color: _isEmailSent
                                  ? AppTheme.lightTheme.colorScheme.tertiary
                                  : AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Center(
                              child: CustomIconWidget(
                                iconName: _isEmailSent ? 'mark_email_read' : 'lock_reset',
                                color: _isEmailSent
                                    ? Colors.white
                                    : AppTheme.lightTheme.primaryColor,
                                size: 40,
                              ),
                            ),
                          ),

                          SizedBox(height: 4.h),

                          // Title and description
                          if (!_isEmailSent) ...[
                            Text(
                              'Forgot Password?',
                              style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.w700,
                              ),
                              textAlign: TextAlign.center,
                            ),

                            SizedBox(height: 2.h),

                            Text(
                              'Don\'t worry! Enter your email address and we\'ll send you a link to reset your password.',
                              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                              ),
                              textAlign: TextAlign.center,
                            ),

                            SizedBox(height: 4.h),

                            // Email Form
                            Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Email Address',
                                    style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  SizedBox(height: 1.h),
                                  TextFormField(
                                    controller: _emailController,
                                    focusNode: _emailFocusNode,
                                    keyboardType: TextInputType.emailAddress,
                                    textInputAction: TextInputAction.done,
                                    onFieldSubmitted: (_) {
                                      if (!_isLoading) {
                                        _handleSendResetLink();
                                      }
                                    },
                                    decoration: InputDecoration(
                                      hintText: 'Enter your email',
                                      prefixIcon: Padding(
                                        padding: EdgeInsets.all(3.w),
                                        child: CustomIconWidget(
                                          iconName: 'email',
                                          color: _emailError != null
                                              ? AppTheme.lightTheme.colorScheme.error
                                              : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                          size: 20,
                                        ),
                                      ),
                                      errorText: _emailError,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            SizedBox(height: 3.h),

                            // General Error Message
                            if (_generalError != null) ...[
                              Container(
                                width: double.infinity,
                                padding: EdgeInsets.all(3.w),
                                decoration: BoxDecoration(
                                  color: AppTheme.lightTheme.colorScheme.error
                                      .withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: AppTheme.lightTheme.colorScheme.error
                                        .withValues(alpha: 0.3),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    CustomIconWidget(
                                      iconName: 'error_outline',
                                      color: AppTheme.lightTheme.colorScheme.error,
                                      size: 20,
                                    ),
                                    SizedBox(width: 2.w),
                                    Expanded(
                                      child: Text(
                                        _generalError!,
                                        style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                                          color: AppTheme.lightTheme.colorScheme.error,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 2.h),
                            ],

                            // Send Reset Link Button
                            SizedBox(
                              width: double.infinity,
                              height: 6.h,
                              child: ElevatedButton(
                                onPressed: _isLoading ? null : _handleSendResetLink,
                                style: AppTheme.lightTheme.elevatedButtonTheme.style?.copyWith(
                                  backgroundColor: WidgetStateProperty.resolveWith((states) {
                                    if (states.contains(WidgetState.disabled)) {
                                      return AppTheme.lightTheme.colorScheme.onSurfaceVariant
                                          .withValues(alpha: 0.3);
                                    }
                                    return AppTheme.lightTheme.primaryColor;
                                  }),
                                ),
                                child: _isLoading
                                    ? SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(
                                            Colors.white,
                                          ),
                                        ),
                                      )
                                    : Text(
                                        'Send Reset Link',
                                        style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                              ),
                            ),
                          ] else ...[
                            // Email sent success state
                            Text(
                              'Check Your Email',
                              style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.w700,
                              ),
                              textAlign: TextAlign.center,
                            ),

                            SizedBox(height: 2.h),

                            RichText(
                              textAlign: TextAlign.center,
                              text: TextSpan(
                                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                ),
                                children: [
                                  const TextSpan(
                                    text: 'We\'ve sent a password reset link to\n',
                                  ),
                                  TextSpan(
                                    text: _emailController.text.trim(),
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: AppTheme.lightTheme.primaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            SizedBox(height: 4.h),

                            // Resend Email Button
                            SizedBox(
                              width: double.infinity,
                              height: 6.h,
                              child: OutlinedButton(
                                onPressed: _handleResendEmail,
                                child: Text(
                                  'Resend Email',
                                  style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                                    color: AppTheme.lightTheme.primaryColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),

                            SizedBox(height: 2.h),

                            // Back to Login Button
                            SizedBox(
                              width: double.infinity,
                              height: 6.h,
                              child: ElevatedButton(
                                onPressed: _handleBackToLogin,
                                child: Text(
                                  'Back to Login',
                                  style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ],

                          const Spacer(),

                          // Footer text
                          if (!_isEmailSent)
                            Padding(
                              padding: EdgeInsets.only(bottom: 2.h),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Remember your password? ',
                                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: _isLoading ? null : _handleBackToLogin,
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      minimumSize: Size.zero,
                                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                    ),
                                    child: Text(
                                      'Sign In',
                                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                        color: AppTheme.lightTheme.primaryColor,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
