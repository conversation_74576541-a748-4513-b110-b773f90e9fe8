import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/biometric_prompt_widget.dart';
import './widgets/login_form_widget.dart';
import './widgets/role_selector_widget.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();

  String _selectedRole = 'Customer';
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  bool _rememberCredentials = false;
  String? _emailError;
  String? _passwordError;
  String? _generalError;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Mock credentials for different roles
  final Map<String, Map<String, String>> _mockCredentials = {
    'Customer': {
      'email': '<EMAIL>',
      'password': 'customer123',
    },
    'Supplier': {
      'email': '<EMAIL>',
      'password': 'supplier123',
    },
    'Agent': {
      'email': '<EMAIL>',
      'password': 'agent123',
    },
    'Admin': {
      'email': '<EMAIL>',
      'password': 'admin123',
    },
  };

  @override
  void initState() {
    super.initState();
    _resetState();
    _initializeAnimations();
    _loadSavedCredentials();
    _setupKeyboardListener();
  }

  void _resetState() {
    // Reset all state variables when screen initializes
    _isLoading = false;
    _emailError = null;
    _passwordError = null;
    _generalError = null;
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeController.forward();
    _slideController.forward();
  }

  void _setupKeyboardListener() {
    _emailFocusNode.addListener(() {
      if (_emailFocusNode.hasFocus) {
        _clearErrors();
      }
    });

    _passwordFocusNode.addListener(() {
      if (_passwordFocusNode.hasFocus) {
        _clearErrors();
      }
    });
  }

  void _loadSavedCredentials() {
    // Simulate loading saved credentials
    // In real app, use SharedPreferences
    setState(() {
      _emailController.text = '';
      _selectedRole = 'Customer';
    });
  }

  void _clearErrors() {
    if (_emailError != null ||
        _passwordError != null ||
        _generalError != null) {
      setState(() {
        _emailError = null;
        _passwordError = null;
        _generalError = null;
      });
    }
  }

  bool _validateInputs() {
    bool isValid = true;

    if (_emailController.text.isEmpty) {
      setState(() {
        _emailError = 'Email is required';
      });
      isValid = false;
    } else if (!_isValidEmail(_emailController.text)) {
      setState(() {
        _emailError = 'Please enter a valid email address';
      });
      isValid = false;
    }

    if (_passwordController.text.isEmpty) {
      setState(() {
        _passwordError = 'Password is required';
      });
      isValid = false;
    } else if (_passwordController.text.length < 6) {
      setState(() {
        _passwordError = 'Password must be at least 6 characters';
      });
      isValid = false;
    }

    return isValid;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  Future<void> _handleLogin() async {
    if (!_validateInputs()) return;

    setState(() {
      _isLoading = true;
      _generalError = null;
    });

    // Simulate network delay
    await Future.delayed(const Duration(seconds: 2));

    // Check credentials
    final roleCredentials = _mockCredentials[_selectedRole];
    if (roleCredentials != null &&
        _emailController.text == roleCredentials['email'] &&
        _passwordController.text == roleCredentials['password']) {
      // Success - trigger haptic feedback
      HapticFeedback.lightImpact();

      if (_rememberCredentials) {
        // Save credentials in real app
      }

      // Show biometric prompt for future logins
      if (mounted) {
        _showBiometricPrompt();
      }

      // Navigate to role-specific home screen
      _navigateToHomeScreen();
    } else {
      setState(() {
        _generalError =
            'Invalid credentials for $_selectedRole role. Please check your email and password.';
        _isLoading = false;
      });
      HapticFeedback.heavyImpact();
    }
  }

  void _showBiometricPrompt() {
    showDialog(
      context: context,
      builder: (context) => BiometricPromptWidget(
        onBiometricEnabled: () {
          Navigator.of(context).pop();
          _navigateToHomeScreen();
        },
        onSkip: () {
          Navigator.of(context).pop();
          _navigateToHomeScreen();
        },
      ),
    );
  }

  void _navigateToHomeScreen() {
    // Reset loading state before navigation
    setState(() {
      _isLoading = false;
    });

    // Navigate based on role
    String route = '/dashboard-screen'; // Default route
    switch (_selectedRole) {
      case 'Customer':
        route = '/customer-dashboard';
        break;
      case 'Supplier':
        route = '/supplier-dashboard';
        break;
      case 'Agent':
        route = '/agent-dashboard';
        break;
      case 'Admin':
        route = '/admin-dashboard';
        break;
    }

    Navigator.pushReplacementNamed(context, route);
  }

  void _handleForgotPassword() {
    Navigator.pushNamed(context, '/forgot-password-screen');
  }

  void _handleSignUp() {
    Navigator.pushNamed(context, '/register-screen');
  }

  // void _handleBackNavigation() {
  //   Navigator.pushReplacementNamed(context, '/splash-screen');
  // }

  @override
  void dispose() {
    // Reset loading state before dispose
    _isLoading = false;

    _fadeController.dispose();
    _slideController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: IntrinsicHeight(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 6.w),
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(height: 4.h),

                          // Back button
                          // Align(
                          //   alignment: Alignment.centerLeft,
                          //   child: IconButton(
                          //     onPressed: _handleBackNavigation,
                          //     icon: CustomIconWidget(
                          //       iconName: 'arrow_back_ios',
                          //       color:
                          //           AppTheme.lightTheme.colorScheme.onSurface,
                          //       size: 24,
                          //     ),
                          //     padding: EdgeInsets.zero,
                          //     constraints: const BoxConstraints(),
                          //   ),
                          // ),

                          SizedBox(height: 2.h),

                          // App Logo
                          Container(
                            width: 20.w,
                            height: 20.w,
                            decoration: BoxDecoration(
                              color: AppTheme.lightTheme.primaryColor,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.lightTheme.primaryColor
                                      .withValues(alpha: 0.3),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Center(
                              child: CustomIconWidget(
                                iconName: 'local_shipping',
                                color: Colors.white,
                                size: 32,
                              ),
                            ),
                          ),

                          SizedBox(height: 3.h),

                          // Welcome text
                          Text(
                            'Welcome Back',
                            style: AppTheme.lightTheme.textTheme.headlineMedium
                                ?.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          SizedBox(height: 1.h),

                          Text(
                            'Sign in to continue to TowTruck Pro',
                            style: AppTheme.lightTheme.textTheme.bodyMedium
                                ?.copyWith(
                              color: AppTheme
                                  .lightTheme.colorScheme.onSurfaceVariant,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          SizedBox(height: 4.h),

                          // Role Selector
                          RoleSelectorWidget(
                            selectedRole: _selectedRole,
                            onRoleChanged: (role) {
                              setState(() {
                                _selectedRole = role;
                                _clearErrors();
                              });
                            },
                          ),

                          SizedBox(height: 3.h),

                          // Login Form
                          Form(
                            key: _formKey,
                            child: LoginFormWidget(
                              emailController: _emailController,
                              passwordController: _passwordController,
                              emailFocusNode: _emailFocusNode,
                              passwordFocusNode: _passwordFocusNode,
                              isPasswordVisible: _isPasswordVisible,
                              emailError: _emailError,
                              passwordError: _passwordError,
                              onPasswordVisibilityToggle: () {
                                setState(() {
                                  _isPasswordVisible = !_isPasswordVisible;
                                });
                              },
                              onFieldSubmitted: () {
                                if (!_isLoading) {
                                  _handleLogin();
                                }
                              },
                            ),
                          ),

                          SizedBox(height: 2.h),

                          // Forgot Password
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed:
                                  _isLoading ? null : _handleForgotPassword,
                              child: Text(
                                'Forgot Password?',
                                style: AppTheme.lightTheme.textTheme.bodyMedium
                                    ?.copyWith(
                                  color: AppTheme.lightTheme.primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),

                          SizedBox(height: 2.h),

                          // General Error Message
                          if (_generalError != null) ...[
                            Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(3.w),
                              decoration: BoxDecoration(
                                color: AppTheme.lightTheme.colorScheme.error
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppTheme.lightTheme.colorScheme.error
                                      .withValues(alpha: 0.3),
                                ),
                              ),
                              child: Row(
                                children: [
                                  CustomIconWidget(
                                    iconName: 'error_outline',
                                    color:
                                        AppTheme.lightTheme.colorScheme.error,
                                    size: 20,
                                  ),
                                  SizedBox(width: 2.w),
                                  Expanded(
                                    child: Text(
                                      _generalError!,
                                      style: AppTheme
                                          .lightTheme.textTheme.bodySmall
                                          ?.copyWith(
                                        color: AppTheme
                                            .lightTheme.colorScheme.error,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 2.h),
                          ],

                          // Login Button
                          SizedBox(
                            width: double.infinity,
                            height: 6.h,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _handleLogin,
                              style: AppTheme
                                  .lightTheme.elevatedButtonTheme.style
                                  ?.copyWith(
                                backgroundColor:
                                    WidgetStateProperty.resolveWith((states) {
                                  if (states.contains(WidgetState.disabled)) {
                                    return AppTheme
                                        .lightTheme.colorScheme.onSurfaceVariant
                                        .withValues(alpha: 0.3);
                                  }
                                  return AppTheme.lightTheme.primaryColor;
                                }),
                              ),
                              child: _isLoading
                                  ? SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                      ),
                                    )
                                  : Text(
                                      'Login',
                                      style: AppTheme
                                          .lightTheme.textTheme.labelLarge
                                          ?.copyWith(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                            ),
                          ),

                          const Spacer(),

                          // Sign Up Link
                          Padding(
                            padding: EdgeInsets.only(bottom: 2.h),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'New user? ',
                                  style: AppTheme
                                      .lightTheme.textTheme.bodyMedium
                                      ?.copyWith(
                                    color: AppTheme.lightTheme.colorScheme
                                        .onSurfaceVariant,
                                  ),
                                ),
                                TextButton(
                                  onPressed: _isLoading ? null : _handleSignUp,
                                  style: TextButton.styleFrom(
                                    padding: EdgeInsets.zero,
                                    minimumSize: Size.zero,
                                    tapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ),
                                  child: Text(
                                    'Sign Up',
                                    style: AppTheme
                                        .lightTheme.textTheme.bodyMedium
                                        ?.copyWith(
                                      color: AppTheme.lightTheme.primaryColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
