import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../../core/app_export.dart';

class AgentOrdersWidget extends StatelessWidget {
  final List<OrderModel> orders;
  final bool isPreview;

  const AgentOrdersWidget({
    Key? key,
    required this.orders,
    this.isPreview = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: orders.length,
      separatorBuilder: (context, index) => SizedBox(height: 2.h),
      itemBuilder: (context, index) {
        final order = orders[index];
        return _buildOrderCard(context, order);
      },
    );
  }

  Widget _buildOrderCard(BuildContext context, OrderModel order) {
    final commission = (order.finalPrice ?? order.estimatedPrice ?? 0) * 0.10;
    
    return GestureDetector(
      onTap: isPreview ? null : () {
        Navigator.pushNamed(
          context,
          '/order-details-screen',
          arguments: {'orderId': order.id},
        );
      },
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: _getStatusColor(order.status).withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: _getServiceColor(order.serviceType).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CustomIconWidget(
                        iconName: _getServiceIcon(order.serviceType),
                        color: _getServiceColor(order.serviceType),
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order.serviceType,
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'Order #${order.id.substring(0, 8)}',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                _buildStatusChip(order.status),
              ],
            ),

            SizedBox(height: 2.h),

            // Customer info
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'person',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Customer #${order.customerId.substring(0, 8)}',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            SizedBox(height: 1.h),

            // Vehicle info
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'directions_car',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Text(
                  '${order.vehicleInfo.make} ${order.vehicleInfo.model} (${order.vehicleInfo.licensePlate})',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            SizedBox(height: 1.h),

            // Location info
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'location_on',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    order.pickupLocation.address,
                    style: AppTheme.lightTheme.textTheme.bodyMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            if (order.description.isNotEmpty) ...[
              SizedBox(height: 1.h),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomIconWidget(
                    iconName: 'description',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 16,
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Text(
                      order.description,
                      style: AppTheme.lightTheme.textTheme.bodyMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],

            SizedBox(height: 2.h),

            // Commission and date info
            Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: Colors.purple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Commission',
                        style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          color: Colors.purple.shade700,
                        ),
                      ),
                      Text(
                        '${AppConstants.currencySymbol}${commission.toStringAsFixed(0)}',
                        style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                          color: Colors.purple.shade700,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        order.isCompleted ? 'Completed' : 'Created',
                        style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        DateFormat('MMM dd, HH:mm').format(
                          order.completedAt ?? order.createdAt,
                        ),
                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Action buttons for active orders
            if (!isPreview && (order.isInProgress || order.isAccepted)) ...[
              SizedBox(height: 2.h),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        _showContactCustomerDialog(context, order);
                      },
                      icon: CustomIconWidget(
                        iconName: 'phone',
                        color: AppTheme.lightTheme.primaryColor,
                        size: 16,
                      ),
                      label: Text(
                        'Contact Customer',
                        style: TextStyle(
                          color: AppTheme.lightTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: AppTheme.lightTheme.primaryColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          '/track-service-screen',
                          arguments: {'orderId': order.id},
                        );
                      },
                      icon: CustomIconWidget(
                        iconName: 'my_location',
                        color: Colors.white,
                        size: 16,
                      ),
                      label: Text(
                        'Track',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.lightTheme.primaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],

            // Performance indicator for completed orders
            if (order.isCompleted) ...[
              SizedBox(height: 1.h),
              Row(
                children: [
                  CustomIconWidget(
                    iconName: 'check_circle',
                    color: Colors.green,
                    size: 16,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    'Commission earned successfully',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 3.w,
        vertical: 1.h,
      ),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        status.toUpperCase(),
        style: TextStyle(
          color: _getStatusColor(status),
          fontSize: 10.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return Colors.orange;
      case AppConstants.orderStatusAccepted:
        return Colors.blue;
      case AppConstants.orderStatusInProgress:
        return Colors.purple;
      case AppConstants.orderStatusCompleted:
        return Colors.green;
      case AppConstants.orderStatusCancelled:
        return Colors.red;
      case AppConstants.orderStatusRejected:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  Color _getServiceColor(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return Colors.red;
      case AppConstants.serviceTypeJumpStart:
        return Colors.orange;
      case AppConstants.serviceTypeFlatTire:
        return Colors.blue;
      case AppConstants.serviceTypeFuelDelivery:
        return Colors.green;
      case AppConstants.serviceTypeLockout:
        return Colors.purple;
      case AppConstants.serviceTypeWinchOut:
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }

  String _getServiceIcon(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return 'local_shipping';
      case AppConstants.serviceTypeJumpStart:
        return 'battery_charging_full';
      case AppConstants.serviceTypeFlatTire:
        return 'tire_repair';
      case AppConstants.serviceTypeFuelDelivery:
        return 'local_gas_station';
      case AppConstants.serviceTypeLockout:
        return 'lock_open';
      case AppConstants.serviceTypeWinchOut:
        return 'construction';
      default:
        return 'build';
    }
  }

  void _showContactCustomerDialog(BuildContext context, OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Customer'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order: ${order.serviceType}'),
            Text('Customer ID: ${order.customerId.substring(0, 8)}'),
            Text('Location: ${order.pickupLocation.address}'),
            SizedBox(height: 2.h),
            const Text('How would you like to contact the customer?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement call functionality
            },
            icon: CustomIconWidget(
              iconName: 'phone',
              color: Colors.white,
              size: 16,
            ),
            label: const Text('Call Customer'),
          ),
        ],
      ),
    );
  }
}
