import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ServiceCardWidget extends StatelessWidget {
  final Function(String) onServiceSelected;

  const ServiceCardWidget({
    Key? key,
    required this.onServiceSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final services = [
      {
        'type': AppConstants.serviceTypeTowing,
        'title': 'Towing',
        'description': 'Vehicle breakdown or accident',
        'icon': 'local_shipping',
        'color': Colors.red,
        'price': 'From ฿1,200',
      },
      {
        'type': AppConstants.serviceTypeJumpStart,
        'title': 'Jump Start',
        'description': 'Dead battery assistance',
        'icon': 'battery_charging_full',
        'color': Colors.orange,
        'price': 'From ฿600',
      },
      {
        'type': AppConstants.serviceTypeFlatTire,
        'title': 'Flat Tire',
        'description': 'Tire change service',
        'icon': 'tire_repair',
        'color': Colors.blue,
        'price': 'From ฿500',
      },
      {
        'type': AppConstants.serviceTypeFuelDelivery,
        'title': 'Fuel Delivery',
        'description': 'Emergency fuel service',
        'icon': 'local_gas_station',
        'color': Colors.green,
        'price': 'From ฿400',
      },
      {
        'type': AppConstants.serviceTypeLockout,
        'title': 'Lockout',
        'description': 'Locked out of vehicle',
        'icon': 'lock_open',
        'color': Colors.purple,
        'price': 'From ฿800',
      },
      {
        'type': AppConstants.serviceTypeWinchOut,
        'title': 'Winch Out',
        'description': 'Vehicle stuck assistance',
        'icon': 'construction',
        'color': Colors.brown,
        'price': 'From ฿1,000',
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 3.w,
        mainAxisSpacing: 2.h,
        childAspectRatio: 0.85,
      ),
      itemCount: services.length,
      itemBuilder: (context, index) {
        final service = services[index];
        return _buildServiceCard(context, service);
      },
    );
  }

  Widget _buildServiceCard(BuildContext context, Map<String, dynamic> service) {
    return GestureDetector(
      onTap: () => onServiceSelected(service['type']),
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon and color header
            Container(
              width: double.infinity,
              height: 12.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    service['color'],
                    service['color'].withValues(alpha: 0.7),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: service['icon'],
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(3.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      service['title'],
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      service['description'],
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          service['price'],
                          style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                            color: service['color'],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 2.w,
                            vertical: 0.5.h,
                          ),
                          decoration: BoxDecoration(
                            color: service['color'].withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: CustomIconWidget(
                            iconName: 'arrow_forward',
                            color: service['color'],
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
