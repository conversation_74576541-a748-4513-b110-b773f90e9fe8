import 'package:towtruck_pro/core/constants/app_constants.dart';

/// Order model for tow truck service requests
class OrderModel {
  final String id;
  final String customerId;
  final String? supplierId;
  final String? agentId;
  final String serviceType;
  final String status;
  final String description;
  final LocationInfo pickupLocation;
  final LocationInfo? dropoffLocation;
  final double? estimatedPrice;
  final double? finalPrice;
  final DateTime createdAt;
  final DateTime? acceptedAt;
  final DateTime? completedAt;
  final DateTime? cancelledAt;
  final String? cancellationReason;
  final VehicleInfo vehicleInfo;
  final List<OrderStatusUpdate> statusUpdates;
  final OrderRating? rating;

  OrderModel({
    required this.id,
    required this.customerId,
    this.supplierId,
    this.agentId,
    required this.serviceType,
    required this.status,
    required this.description,
    required this.pickupLocation,
    this.dropoffLocation,
    this.estimatedPrice,
    this.finalPrice,
    required this.createdAt,
    this.acceptedAt,
    this.completedAt,
    this.cancelledAt,
    this.cancellationReason,
    required this.vehicleInfo,
    this.statusUpdates = const [],
    this.rating,
  });

  bool get isPending => status == AppConstants.orderStatusPending;
  bool get isAccepted => status == AppConstants.orderStatusAccepted;
  bool get isInProgress => status == AppConstants.orderStatusInProgress;
  bool get isCompleted => status == AppConstants.orderStatusCompleted;
  bool get isCancelled => status == AppConstants.orderStatusCancelled;
  bool get isRejected => status == AppConstants.orderStatusRejected;

  bool get canBeCancelled => isPending || isAccepted;
  bool get canBeRated => isCompleted && rating == null;

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'] ?? '',
      customerId: json['customer_id'] ?? '',
      supplierId: json['supplier_id'],
      agentId: json['agent_id'],
      serviceType: json['service_type'] ?? AppConstants.serviceTypeTowing,
      status: json['status'] ?? AppConstants.orderStatusPending,
      description: json['description'] ?? '',
      pickupLocation: LocationInfo.fromJson(json['pickup_location'] ?? {}),
      dropoffLocation: json['dropoff_location'] != null 
          ? LocationInfo.fromJson(json['dropoff_location']) 
          : null,
      estimatedPrice: json['estimated_price']?.toDouble(),
      finalPrice: json['final_price']?.toDouble(),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      acceptedAt: json['accepted_at'] != null ? DateTime.parse(json['accepted_at']) : null,
      completedAt: json['completed_at'] != null ? DateTime.parse(json['completed_at']) : null,
      cancelledAt: json['cancelled_at'] != null ? DateTime.parse(json['cancelled_at']) : null,
      cancellationReason: json['cancellation_reason'],
      vehicleInfo: VehicleInfo.fromJson(json['vehicle_info'] ?? {}),
      statusUpdates: (json['status_updates'] as List<dynamic>?)
          ?.map((e) => OrderStatusUpdate.fromJson(e))
          .toList() ?? [],
      rating: json['rating'] != null ? OrderRating.fromJson(json['rating']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'supplier_id': supplierId,
      'agent_id': agentId,
      'service_type': serviceType,
      'status': status,
      'description': description,
      'pickup_location': pickupLocation.toJson(),
      'dropoff_location': dropoffLocation?.toJson(),
      'estimated_price': estimatedPrice,
      'final_price': finalPrice,
      'created_at': createdAt.toIso8601String(),
      'accepted_at': acceptedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'cancelled_at': cancelledAt?.toIso8601String(),
      'cancellation_reason': cancellationReason,
      'vehicle_info': vehicleInfo.toJson(),
      'status_updates': statusUpdates.map((e) => e.toJson()).toList(),
      'rating': rating?.toJson(),
    };
  }

  OrderModel copyWith({
    String? id,
    String? customerId,
    String? supplierId,
    String? agentId,
    String? serviceType,
    String? status,
    String? description,
    LocationInfo? pickupLocation,
    LocationInfo? dropoffLocation,
    double? estimatedPrice,
    double? finalPrice,
    DateTime? createdAt,
    DateTime? acceptedAt,
    DateTime? completedAt,
    DateTime? cancelledAt,
    String? cancellationReason,
    VehicleInfo? vehicleInfo,
    List<OrderStatusUpdate>? statusUpdates,
    OrderRating? rating,
  }) {
    return OrderModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      supplierId: supplierId ?? this.supplierId,
      agentId: agentId ?? this.agentId,
      serviceType: serviceType ?? this.serviceType,
      status: status ?? this.status,
      description: description ?? this.description,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      dropoffLocation: dropoffLocation ?? this.dropoffLocation,
      estimatedPrice: estimatedPrice ?? this.estimatedPrice,
      finalPrice: finalPrice ?? this.finalPrice,
      createdAt: createdAt ?? this.createdAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      completedAt: completedAt ?? this.completedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      vehicleInfo: vehicleInfo ?? this.vehicleInfo,
      statusUpdates: statusUpdates ?? this.statusUpdates,
      rating: rating ?? this.rating,
    );
  }
}

/// Location information model
class LocationInfo {
  final double latitude;
  final double longitude;
  final String address;
  final String? city;
  final String? state;
  final String? zipCode;
  final String? landmark;

  LocationInfo({
    required this.latitude,
    required this.longitude,
    required this.address,
    this.city,
    this.state,
    this.zipCode,
    this.landmark,
  });

  factory LocationInfo.fromJson(Map<String, dynamic> json) {
    return LocationInfo(
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'] ?? '',
      city: json['city'],
      state: json['state'],
      zipCode: json['zip_code'],
      landmark: json['landmark'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'state': state,
      'zip_code': zipCode,
      'landmark': landmark,
    };
  }
}

/// Vehicle information model
class VehicleInfo {
  final String make;
  final String model;
  final String year;
  final String color;
  final String licensePlate;
  final String? vehicleType;

  VehicleInfo({
    required this.make,
    required this.model,
    required this.year,
    required this.color,
    required this.licensePlate,
    this.vehicleType,
  });

  factory VehicleInfo.fromJson(Map<String, dynamic> json) {
    return VehicleInfo(
      make: json['make'] ?? '',
      model: json['model'] ?? '',
      year: json['year'] ?? '',
      color: json['color'] ?? '',
      licensePlate: json['license_plate'] ?? '',
      vehicleType: json['vehicle_type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'make': make,
      'model': model,
      'year': year,
      'color': color,
      'license_plate': licensePlate,
      'vehicle_type': vehicleType,
    };
  }
}

/// Order status update model
class OrderStatusUpdate {
  final String id;
  final String orderId;
  final String status;
  final String? message;
  final DateTime timestamp;
  final String? updatedBy;

  OrderStatusUpdate({
    required this.id,
    required this.orderId,
    required this.status,
    this.message,
    required this.timestamp,
    this.updatedBy,
  });

  factory OrderStatusUpdate.fromJson(Map<String, dynamic> json) {
    return OrderStatusUpdate(
      id: json['id'] ?? '',
      orderId: json['order_id'] ?? '',
      status: json['status'] ?? '',
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      updatedBy: json['updated_by'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'status': status,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'updated_by': updatedBy,
    };
  }
}

/// Order rating model
class OrderRating {
  final String id;
  final String orderId;
  final String customerId;
  final String supplierId;
  final int rating;
  final String? comment;
  final DateTime createdAt;

  OrderRating({
    required this.id,
    required this.orderId,
    required this.customerId,
    required this.supplierId,
    required this.rating,
    this.comment,
    required this.createdAt,
  });

  factory OrderRating.fromJson(Map<String, dynamic> json) {
    return OrderRating(
      id: json['id'] ?? '',
      orderId: json['order_id'] ?? '',
      customerId: json['customer_id'] ?? '',
      supplierId: json['supplier_id'] ?? '',
      rating: json['rating'] ?? 0,
      comment: json['comment'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'customer_id': customerId,
      'supplier_id': supplierId,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
