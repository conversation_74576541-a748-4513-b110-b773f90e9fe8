import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class LocationPickerWidget extends StatefulWidget {
  final LocationInfo? pickupLocation;
  final LocationInfo? dropoffLocation;
  final bool needsDropoff;
  final Function(LocationInfo) onPickupLocationChanged;
  final Function(LocationInfo?) onDropoffLocationChanged;
  final Function(bool) onNeedsDropoffChanged;

  const LocationPickerWidget({
    Key? key,
    this.pickupLocation,
    this.dropoffLocation,
    required this.needsDropoff,
    required this.onPickupLocationChanged,
    required this.onDropoffLocationChanged,
    required this.onNeedsDropoffChanged,
  }) : super(key: key);

  @override
  State<LocationPickerWidget> createState() => _LocationPickerWidgetState();
}

class _LocationPickerWidgetState extends State<LocationPickerWidget> {
  final TextEditingController _pickupController = TextEditingController();
  final TextEditingController _dropoffController = TextEditingController();
  final LocationService _locationService = LocationService();

  bool _isLoadingCurrentLocation = false;

  @override
  void initState() {
    super.initState();
    if (widget.pickupLocation != null) {
      _pickupController.text = widget.pickupLocation!.address;
    }
    if (widget.dropoffLocation != null) {
      _dropoffController.text = widget.dropoffLocation!.address;
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingCurrentLocation = true;
    });

    try {
      final position = await _locationService.getCurrentPosition(forceRefresh: true);
      if (position != null) {
        final address = await _locationService.getAddressFromCoordinates(
          position.latitude,
          position.longitude,
        );

        if (address != null) {
          final locationInfo = LocationInfo(
            latitude: position.latitude,
            longitude: position.longitude,
            address: address,
          );

          setState(() {
            _pickupController.text = address;
          });

          widget.onPickupLocationChanged(locationInfo);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to get current location: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoadingCurrentLocation = false;
      });
    }
  }

  void _showLocationPicker({required bool isPickup}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildLocationPickerModal(isPickup: isPickup),
    );
  }

  Widget _buildLocationPickerModal({required bool isPickup}) {
    return Container(
      height: 80.h,
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: EdgeInsets.only(top: 2.h),
            width: 12.w,
            height: 0.5.h,
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(10),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(4.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  isPickup ? 'Select Pickup Location' : 'Select Dropoff Location',
                  style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: CustomIconWidget(
                    iconName: 'close',
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),

          // Search field
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            child: TextFormField(
              decoration: InputDecoration(
                hintText: 'Search for a location...',
                prefixIcon: Padding(
                  padding: EdgeInsets.all(3.w),
                  child: CustomIconWidget(
                    iconName: 'search',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ),
              ),
              onChanged: (value) {
                // TODO: Implement location search
              },
            ),
          ),

          SizedBox(height: 2.h),

          // Current location option (only for pickup)
          if (isPickup)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: ListTile(
                leading: Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CustomIconWidget(
                    iconName: 'my_location',
                    color: AppTheme.lightTheme.primaryColor,
                    size: 20,
                  ),
                ),
                title: Text(
                  'Use Current Location',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  'Automatically detect your location',
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  ),
                ),
                trailing: _isLoadingCurrentLocation
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : CustomIconWidget(
                        iconName: 'arrow_forward_ios',
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        size: 16,
                      ),
                onTap: _isLoadingCurrentLocation ? null : () {
                  Navigator.pop(context);
                  _getCurrentLocation();
                },
              ),
            ),

          // Divider
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
            child: Divider(
              color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),

          // Recent locations
          Expanded(
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              children: [
                Text(
                  'Recent Locations',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                
                // Mock recent locations
                ..._buildRecentLocationsList(isPickup),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildRecentLocationsList(bool isPickup) {
    final recentLocations = [
      {
        'name': 'Home',
        'address': '123 Sukhumvit Road, Bangkok 10110',
        'icon': 'home',
      },
      {
        'name': 'Office',
        'address': '456 Silom Road, Bangkok 10500',
        'icon': 'work',
      },
      {
        'name': 'Central World',
        'address': '999 Rama I Road, Pathumwan, Bangkok 10330',
        'icon': 'shopping_mall',
      },
      {
        'name': 'Suvarnabhumi Airport',
        'address': '999 Bang Na-Trat Road, Bang Phli, Samut Prakan 10540',
        'icon': 'flight',
      },
    ];

    return recentLocations.map((location) {
      return ListTile(
        leading: Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: CustomIconWidget(
            iconName: location['icon']!,
            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
        ),
        title: Text(
          location['name']!,
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          location['address']!,
          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        onTap: () {
          Navigator.pop(context);
          
          final locationInfo = LocationInfo(
            latitude: 13.7563, // Mock coordinates
            longitude: 100.5018,
            address: location['address']!,
          );

          if (isPickup) {
            setState(() {
              _pickupController.text = location['address']!;
            });
            widget.onPickupLocationChanged(locationInfo);
          } else {
            setState(() {
              _dropoffController.text = location['address']!;
            });
            widget.onDropoffLocationChanged(locationInfo);
          }
        },
      );
    }).toList();
  }

  @override
  void dispose() {
    _pickupController.dispose();
    _dropoffController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Pickup location
        Text(
          'Pickup Location *',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        GestureDetector(
          onTap: () => _showLocationPicker(isPickup: true),
          child: Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.cardColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CustomIconWidget(
                    iconName: 'location_on',
                    color: Colors.green,
                    size: 20,
                  ),
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Text(
                    widget.pickupLocation?.address ?? 'Select pickup location',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      color: widget.pickupLocation != null
                          ? AppTheme.lightTheme.colorScheme.onSurface
                          : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                CustomIconWidget(
                  iconName: 'arrow_forward_ios',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 3.h),

        // Needs dropoff toggle
        Row(
          children: [
            Switch(
              value: widget.needsDropoff,
              onChanged: widget.onNeedsDropoffChanged,
              activeColor: AppTheme.lightTheme.primaryColor,
            ),
            SizedBox(width: 2.w),
            Expanded(
              child: Text(
                'Need dropoff location (e.g., for towing)',
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),

        // Dropoff location (if needed)
        if (widget.needsDropoff) ...[
          SizedBox(height: 2.h),
          Text(
            'Dropoff Location *',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 1.h),
          GestureDetector(
            onTap: () => _showLocationPicker(isPickup: false),
            child: Container(
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: CustomIconWidget(
                      iconName: 'flag',
                      color: Colors.red,
                      size: 20,
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Text(
                      widget.dropoffLocation?.address ?? 'Select dropoff location',
                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                        color: widget.dropoffLocation != null
                            ? AppTheme.lightTheme.colorScheme.onSurface
                            : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  CustomIconWidget(
                    iconName: 'arrow_forward_ios',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }
}
