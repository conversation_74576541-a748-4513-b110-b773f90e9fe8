import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../../core/app_export.dart';

class RecentActivitiesWidget extends StatelessWidget {
  final List<Map<String, dynamic>> activities;

  const RecentActivitiesWidget({
    Key? key,
    required this.activities,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: 'history',
                  color: AppTheme.lightTheme.primaryColor,
                  size: 20,
                ),
              ),
              SizedBox(width: 3.w),
              Text(
                'Recent Activities',
                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  // TODO: Navigate to full activity log
                },
                child: Text(
                  'View All',
                  style: TextStyle(
                    color: AppTheme.lightTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Activities list
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: activities.length,
            separatorBuilder: (context, index) => SizedBox(height: 2.h),
            itemBuilder: (context, index) {
              final activity = activities[index];
              return _buildActivityItem(activity);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(Map<String, dynamic> activity) {
    final activityType = activity['type'] as String;
    final timestamp = activity['timestamp'] as DateTime;
    final timeAgo = DateTime.now().difference(timestamp);
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Activity icon
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: _getActivityColor(activityType).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomIconWidget(
            iconName: _getActivityIcon(activityType),
            color: _getActivityColor(activityType),
            size: 18,
          ),
        ),
        
        SizedBox(width: 3.w),
        
        // Activity content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                activity['description'],
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 0.5.h),
              Row(
                children: [
                  Text(
                    activity['user'],
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    ' • ',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    _formatTimeAgo(timeAgo),
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // Activity status indicator
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: 2.w,
            vertical: 0.5.h,
          ),
          decoration: BoxDecoration(
            color: _getActivityColor(activityType).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getActivityStatus(activityType),
            style: TextStyle(
              color: _getActivityColor(activityType),
              fontSize: 9.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Color _getActivityColor(String activityType) {
    switch (activityType) {
      case 'order_completed':
        return Colors.green;
      case 'order_created':
        return Colors.blue;
      case 'order_cancelled':
        return Colors.red;
      case 'agent_approved':
        return Colors.purple;
      case 'agent_rejected':
        return Colors.orange;
      case 'supplier_registered':
        return Colors.teal;
      case 'user_registered':
        return Colors.indigo;
      case 'payment_received':
        return Colors.green;
      case 'system_alert':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getActivityIcon(String activityType) {
    switch (activityType) {
      case 'order_completed':
        return 'check_circle';
      case 'order_created':
        return 'add_circle';
      case 'order_cancelled':
        return 'cancel';
      case 'agent_approved':
        return 'how_to_reg';
      case 'agent_rejected':
        return 'person_remove';
      case 'supplier_registered':
        return 'local_shipping';
      case 'user_registered':
        return 'person_add';
      case 'payment_received':
        return 'payment';
      case 'system_alert':
        return 'warning';
      default:
        return 'info';
    }
  }

  String _getActivityStatus(String activityType) {
    switch (activityType) {
      case 'order_completed':
        return 'SUCCESS';
      case 'order_created':
        return 'NEW';
      case 'order_cancelled':
        return 'CANCELLED';
      case 'agent_approved':
        return 'APPROVED';
      case 'agent_rejected':
        return 'REJECTED';
      case 'supplier_registered':
        return 'REGISTERED';
      case 'user_registered':
        return 'NEW USER';
      case 'payment_received':
        return 'PAID';
      case 'system_alert':
        return 'ALERT';
      default:
        return 'INFO';
    }
  }

  String _formatTimeAgo(Duration duration) {
    if (duration.inMinutes < 1) {
      return 'Just now';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ago';
    } else if (duration.inHours < 24) {
      return '${duration.inHours}h ago';
    } else {
      return '${duration.inDays}d ago';
    }
  }
}
