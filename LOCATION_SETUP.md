# 📍 Location Permission Setup Guide

## ✅ **Permissions ที่เพิ่มแล้ว**

### 🤖 **Android Permissions (AndroidManifest.xml)**
```xml
<!-- Location permissions -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- Background location permission (for Android 10+) -->
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

<!-- Foreground service permission (for location tracking) -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
```

### 🍎 **iOS Permissions (Info.plist)**
```xml
<!-- Location permissions -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>This app needs location access to help you request tow truck services and show your current location on the map.</string>

<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>This app needs location access to help you request tow truck services and track your location during service.</string>

<key>NSLocationAlwaysUsageDescription</key>
<string>This app needs location access to help you request tow truck services and track your location during service.</string>
```

## 🔧 **Location Service Implementation**

### 📱 **Features ที่ทำงานแล้ว:**
- ✅ **Auto check permission** เมื่อเข้าแอป
- ✅ **Request permission** ถ้ายังไม่ได้อนุญาต
- ✅ **Check location service** ว่าเปิดอยู่หรือไม่
- ✅ **Handle permission denied** แสดง dialog
- ✅ **Handle permanently denied** เปิด app settings
- ✅ **Handle service disabled** เปิด location settings
- ✅ **Get current position** ด้วย high accuracy
- ✅ **Show location on map** ด้วย marker
- ✅ **Real-time status** แสดงใน header

### 🎯 **Permission Flow:**
1. **App Start** → Check location service enabled
2. **Service Disabled** → Show dialog → Open location settings
3. **Service Enabled** → Check permission
4. **Permission Denied** → Request permission
5. **Permission Granted** → Get current location
6. **Location Success** → Show map + marker
7. **Any Error** → Show appropriate dialog

### 🚨 **Error Handling:**
- **Location Service Disabled** → Dialog + Open Settings
- **Permission Denied** → Dialog + Try Again
- **Permission Permanently Denied** → Dialog + Open App Settings
- **Location Error** → Show error message

## 🗺️ **Google Maps Setup**

### 🔑 **API Key Required:**
1. ไปที่ [Google Cloud Console](https://console.cloud.google.com/)
2. สร้าง project ใหม่หรือเลือก project ที่มี
3. Enable **Maps SDK for Android** และ **Maps SDK for iOS**
4. สร้าง API Key
5. แทนที่ `YOUR_GOOGLE_MAPS_API_KEY_HERE` ใน:
   - `android/app/src/main/AndroidManifest.xml`
   - `ios/Runner/AppDelegate.swift` (ถ้าต้องการ)

### 📝 **Android API Key:**
```xml
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_ACTUAL_API_KEY_HERE"/>
```

## 🧪 **Testing Location**

### 📱 **Android Emulator:**
1. เปิด Extended Controls (⋯)
2. ไป Location tab
3. ตั้งค่า latitude/longitude
4. กด "Send"

### 📱 **iOS Simulator:**
1. Device → Location → Custom Location
2. ใส่ latitude/longitude
3. กด OK

### 🏃‍♂️ **Real Device:**
1. เปิด Location Service ในเครื่อง
2. อนุญาต permission ให้แอป
3. ทดสอบใน environment จริง

## 🔍 **Troubleshooting**

### ❌ **Location ไม่ทำงาน:**
1. ตรวจสอบ permissions ใน AndroidManifest.xml และ Info.plist
2. ตรวจสอบ Google Maps API Key
3. ตรวจสอบ Location Service เปิดอยู่
4. ตรวจสอบ App Permission ในเครื่อง

### ❌ **Map ไม่แสดง:**
1. ตรวจสอบ Google Maps API Key
2. ตรวจสอบ internet connection
3. ตรวจสอบ API quota ใน Google Cloud

### ❌ **Permission Dialog ไม่ขึ้น:**
1. ลบแอปแล้วติดตั้งใหม่
2. ตรวจสอบ permission ใน device settings
3. ตรวจสอบ code ใน _enableLocation()

## 📋 **Dependencies ที่ใช้**
```yaml
dependencies:
  geolocator: ^14.0.2          # Location services
  google_maps_flutter: ^2.5.0  # Google Maps
  geocoding: ^4.0.0            # Address geocoding
```

## 🎉 **Ready to Use!**
Location permissions และ services พร้อมใช้งานแล้ว! 🚀

Customer Dashboard จะ:
- ✅ ขอ permission อัตโนมัติ
- ✅ แสดง Google Map เมื่อได้ permission
- ✅ แสดง current location marker
- ✅ ให้เลือกจุดหมายได้
- ✅ จัดการ error ทุกกรณี
