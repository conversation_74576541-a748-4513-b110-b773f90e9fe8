import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class SupplierRegisterForm extends StatefulWidget {
  final VoidCallback onBack;

  const SupplierRegisterForm({Key? key, required this.onBack}) : super(key: key);

  @override
  State<SupplierRegisterForm> createState() => _SupplierRegisterFormState();
}

class _SupplierRegisterFormState extends State<SupplierRegisterForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  
  // Controllers
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _companyController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final TextEditingController _licenseController = TextEditingController();
  final TextEditingController _serviceAreaController = TextEditingController();

  // Focus nodes
  final FocusNode _firstNameFocusNode = FocusNode();
  final FocusNode _lastNameFocusNode = FocusNode();
  final FocusNode _companyFocusNode = FocusNode();
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _confirmPasswordFocusNode = FocusNode();
  final FocusNode _licenseFocusNode = FocusNode();
  final FocusNode _serviceAreaFocusNode = FocusNode();

  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _agreeToTerms = false;
  bool _isLoading = false;

  List<String> _selectedServiceTypes = [];
  final List<String> _serviceTypes = [
    AppConstants.serviceTypeTowing,
    AppConstants.serviceTypeJumpStart,
    AppConstants.serviceTypeFlatTire,
    AppConstants.serviceTypeFuelDelivery,
    AppConstants.serviceTypeLockout,
    AppConstants.serviceTypeWinchOut,
  ];

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _companyController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _licenseController.dispose();
    _serviceAreaController.dispose();
    
    _firstNameFocusNode.dispose();
    _lastNameFocusNode.dispose();
    _companyFocusNode.dispose();
    _phoneFocusNode.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    _licenseFocusNode.dispose();
    _serviceAreaFocusNode.dispose();
    super.dispose();
  }

  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'กรุณากรอกข้อมูล';
    }
    if (value.length < 2) {
      return 'ต้องมีอย่างน้อย 2 ตัวอักษร';
    }
    return null;
  }

  String? _validateCompany(String? value) {
    if (value == null || value.isEmpty) {
      return 'กรุณากรอกชื่อบริษัท';
    }
    if (value.length < 3) {
      return 'ชื่อบริษัทต้องมีอย่างน้อย 3 ตัวอักษร';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'กรุณากรอกเบอร์โทรศัพท์';
    }
    if (!RegExp(r'^[0-9]{10}$').hasMatch(value.replaceAll('-', ''))) {
      return 'เบอร์โทรศัพท์ไม่ถูกต้อง';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'กรุณากรอกอีเมล';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'รูปแบบอีเมลไม่ถูกต้อง';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'กรุณากรอกรหัสผ่าน';
    }
    if (value.length < 6) {
      return 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'กรุณายืนยันรหัสผ่าน';
    }
    if (value != _passwordController.text) {
      return 'รหัสผ่านไม่ตรงกัน';
    }
    return null;
  }

  String? _validateLicense(String? value) {
    if (value == null || value.isEmpty) {
      return 'กรุณากรอกเลขบัตร/ใบอนุญาติ';
    }
    if (value.length < 5) {
      return 'เลขบัตร/ใบอนุญาติไม่ถูกต้อง';
    }
    return null;
  }

  String? _validateServiceArea(String? value) {
    if (value == null || value.isEmpty) {
      return 'กรุณากรอกพื้นที่ให้บริการ';
    }
    return null;
  }

  Future<void> _handleRegister() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedServiceTypes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('กรุณาเลือกประเภทบริการอย่างน้อย 1 ประเภท'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (!_agreeToTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('กรุณายอมรับข้อตกลงและเงื่อนไข'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        Navigator.pushNamed(
          context,
          AppRoutes.otpVerificationScreen,
          arguments: {
            'email': _emailController.text,
            'type': 'registration',
          },
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Color _getServiceColor(String serviceType) {
    switch (serviceType) {
      case AppConstants.serviceTypeTowing:
        return Colors.red;
      case AppConstants.serviceTypeJumpStart:
        return Colors.orange;
      case AppConstants.serviceTypeFlatTire:
        return Colors.blue;
      case AppConstants.serviceTypeFuelDelivery:
        return Colors.green;
      case AppConstants.serviceTypeLockout:
        return Colors.purple;
      case AppConstants.serviceTypeWinchOut:
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 6.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 4.h),

                // Back button and title
                Row(
                  children: [
                    IconButton(
                      onPressed: widget.onBack,
                      icon: CustomIconWidget(
                        iconName: 'arrow_back_ios',
                        color: AppTheme.lightTheme.colorScheme.onSurface,
                        size: 24,
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      'Supplier Registration',
                      style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 4.h),

                // First Name
                Text(
                  'ชื่อ *',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                TextFormField(
                  controller: _firstNameController,
                  focusNode: _firstNameFocusNode,
                  validator: _validateName,
                  decoration: InputDecoration(
                    hintText: 'กรอกชื่อ',
                    prefixIcon: CustomIconWidget(
                      iconName: 'person',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) => _lastNameFocusNode.requestFocus(),
                ),

                SizedBox(height: 2.h),

                // Last Name
                Text(
                  'นามสกุล *',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                TextFormField(
                  controller: _lastNameController,
                  focusNode: _lastNameFocusNode,
                  validator: _validateName,
                  decoration: InputDecoration(
                    hintText: 'กรอกนามสกุล',
                    prefixIcon: CustomIconWidget(
                      iconName: 'person',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) => _companyFocusNode.requestFocus(),
                ),

                SizedBox(height: 2.h),

                // Company
                Text(
                  'บริษัท *',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                TextFormField(
                  controller: _companyController,
                  focusNode: _companyFocusNode,
                  validator: _validateCompany,
                  decoration: InputDecoration(
                    hintText: 'กรอกชื่อบริษัท',
                    prefixIcon: CustomIconWidget(
                      iconName: 'business',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) => _phoneFocusNode.requestFocus(),
                ),

                SizedBox(height: 2.h),

                // Phone
                Text(
                  'เบอร์โทรศัพท์ *',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                TextFormField(
                  controller: _phoneController,
                  focusNode: _phoneFocusNode,
                  validator: _validatePhone,
                  keyboardType: TextInputType.phone,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(10),
                  ],
                  decoration: InputDecoration(
                    hintText: '0812345678',
                    prefixIcon: CustomIconWidget(
                      iconName: 'phone',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) => _emailFocusNode.requestFocus(),
                ),

                SizedBox(height: 2.h),

                // Email
                Text(
                  'อีเมล *',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                TextFormField(
                  controller: _emailController,
                  focusNode: _emailFocusNode,
                  validator: _validateEmail,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    hintText: '<EMAIL>',
                    prefixIcon: CustomIconWidget(
                      iconName: 'email',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) => _passwordFocusNode.requestFocus(),
                ),

                SizedBox(height: 2.h),

                // Password
                Text(
                  'รหัสผ่าน *',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                TextFormField(
                  controller: _passwordController,
                  focusNode: _passwordFocusNode,
                  validator: _validatePassword,
                  obscureText: !_isPasswordVisible,
                  decoration: InputDecoration(
                    hintText: 'กรอกรหัสผ่าน',
                    prefixIcon: CustomIconWidget(
                      iconName: 'lock',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                      icon: CustomIconWidget(
                        iconName: _isPasswordVisible ? 'visibility_off' : 'visibility',
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        size: 20,
                      ),
                    ),
                  ),
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) => _confirmPasswordFocusNode.requestFocus(),
                ),

                SizedBox(height: 2.h),

                // Confirm Password
                Text(
                  'ยืนยันรหัสผ่าน *',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                TextFormField(
                  controller: _confirmPasswordController,
                  focusNode: _confirmPasswordFocusNode,
                  validator: _validateConfirmPassword,
                  obscureText: !_isConfirmPasswordVisible,
                  decoration: InputDecoration(
                    hintText: 'ยืนยันรหัสผ่าน',
                    prefixIcon: CustomIconWidget(
                      iconName: 'lock',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                        });
                      },
                      icon: CustomIconWidget(
                        iconName: _isConfirmPasswordVisible ? 'visibility_off' : 'visibility',
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        size: 20,
                      ),
                    ),
                  ),
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) => _licenseFocusNode.requestFocus(),
                ),

                SizedBox(height: 2.h),

                // License
                Text(
                  'เลขบัตร/ใบอนุญาติ *',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                TextFormField(
                  controller: _licenseController,
                  focusNode: _licenseFocusNode,
                  validator: _validateLicense,
                  decoration: InputDecoration(
                    hintText: 'กรอกเลขบัตร/ใบอนุญาติ',
                    prefixIcon: CustomIconWidget(
                      iconName: 'badge',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (_) => _serviceAreaFocusNode.requestFocus(),
                ),

                SizedBox(height: 2.h),

                // Service Types
                Text(
                  'ประเภทบริการ *',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                Wrap(
                  spacing: 2.w,
                  runSpacing: 1.h,
                  children: _serviceTypes.map((serviceType) {
                    final isSelected = _selectedServiceTypes.contains(serviceType);
                    final color = _getServiceColor(serviceType);
                    
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          if (isSelected) {
                            _selectedServiceTypes.remove(serviceType);
                          } else {
                            _selectedServiceTypes.add(serviceType);
                          }
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 3.w,
                          vertical: 1.h,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected ? color : color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: color,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          serviceType,
                          style: TextStyle(
                            color: isSelected ? Colors.white : color,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),

                SizedBox(height: 2.h),

                // Service Area
                Text(
                  'พื้นที่ให้บริการ *',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.h),
                TextFormField(
                  controller: _serviceAreaController,
                  focusNode: _serviceAreaFocusNode,
                  validator: _validateServiceArea,
                  maxLines: 2,
                  decoration: InputDecoration(
                    hintText: 'เช่น กรุงเทพฯ และปริมณฑล',
                    prefixIcon: Padding(
                      padding: EdgeInsets.only(top: 0.5.h),
                      child: CustomIconWidget(
                        iconName: 'location_on',
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        size: 20,
                      ),
                    ),
                  ),
                  textInputAction: TextInputAction.done,
                ),

                SizedBox(height: 3.h),

                // Terms and conditions
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Checkbox(
                      value: _agreeToTerms,
                      onChanged: (value) {
                        setState(() {
                          _agreeToTerms = value ?? false;
                        });
                      },
                      activeColor: Colors.green,
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _agreeToTerms = !_agreeToTerms;
                          });
                        },
                        child: Padding(
                          padding: EdgeInsets.only(top: 1.h),
                          child: RichText(
                            text: TextSpan(
                              style: AppTheme.lightTheme.textTheme.bodySmall,
                              children: [
                                const TextSpan(text: 'ฉันยอมรับ'),
                                TextSpan(
                                  text: 'ข้อตกลงและเงื่อนไข',
                                  style: TextStyle(
                                    color: Colors.green,
                                    fontWeight: FontWeight.w600,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                                const TextSpan(text: ' และ'),
                                TextSpan(
                                  text: 'นโยบายความเป็นส่วนตัว',
                                  style: TextStyle(
                                    color: Colors.green,
                                    fontWeight: FontWeight.w600,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 4.h),

                // Register button
                SizedBox(
                  width: double.infinity,
                  height: 6.h,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _handleRegister,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            'สมัครสมาชิก',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),

                SizedBox(height: 4.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
