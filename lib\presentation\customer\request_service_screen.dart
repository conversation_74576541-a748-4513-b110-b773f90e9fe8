import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import 'widgets/service_type_selector_widget.dart';
import 'widgets/location_picker_widget.dart';
import 'widgets/vehicle_info_widget.dart';

class RequestServiceScreen extends StatefulWidget {
  const RequestServiceScreen({Key? key}) : super(key: key);

  @override
  State<RequestServiceScreen> createState() => _RequestServiceScreenState();
}

class _RequestServiceScreenState extends State<RequestServiceScreen>
    with TickerProviderStateMixin {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _makeController = TextEditingController();
  final TextEditingController _modelController = TextEditingController();
  final TextEditingController _yearController = TextEditingController();
  final TextEditingController _colorController = TextEditingController();
  final TextEditingController _licensePlateController = TextEditingController();

  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  String? _selectedServiceType;
  LocationInfo? _pickupLocation;
  LocationInfo? _dropoffLocation;
  bool _needsDropoff = false;
  bool _isLoading = false;
  String? _generalError;

  int _currentStep = 0;
  final int _totalSteps = 4;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkArguments();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  void _checkArguments() {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null && args['serviceType'] != null) {
      setState(() {
        _selectedServiceType = args['serviceType'];
        _currentStep = 1; // Skip service type selection
      });
    }
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  bool _canProceedToNextStep() {
    switch (_currentStep) {
      case 0:
        return _selectedServiceType != null;
      case 1:
        return _pickupLocation != null;
      case 2:
        return _makeController.text.isNotEmpty &&
               _modelController.text.isNotEmpty &&
               _yearController.text.isNotEmpty &&
               _colorController.text.isNotEmpty &&
               _licensePlateController.text.isNotEmpty;
      case 3:
        return true; // Review step
      default:
        return false;
    }
  }

  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _generalError = null;
    });

    try {
      // Create order model
      final order = OrderModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        customerId: 'current_user_id', // TODO: Get from auth service
        serviceType: _selectedServiceType!,
        status: AppConstants.orderStatusPending,
        description: _descriptionController.text.trim(),
        pickupLocation: _pickupLocation!,
        dropoffLocation: _dropoffLocation,
        vehicleInfo: VehicleInfo(
          make: _makeController.text.trim(),
          model: _modelController.text.trim(),
          year: _yearController.text.trim(),
          color: _colorController.text.trim(),
          licensePlate: _licensePlateController.text.trim(),
        ),
        createdAt: DateTime.now(),
      );

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Success - trigger haptic feedback
      HapticFeedback.lightImpact();

      if (mounted) {
        // Navigate to tracking screen
        Navigator.pushReplacementNamed(
          context,
          AppRoutes.trackServiceScreen,
          arguments: {'orderId': order.id},
        );
      }
    } catch (e) {
      setState(() {
        _generalError = 'Failed to submit request. Please try again.';
        _isLoading = false;
      });
      HapticFeedback.heavyImpact();
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _descriptionController.dispose();
    _makeController.dispose();
    _modelController.dispose();
    _yearController.dispose();
    _colorController.dispose();
    _licensePlateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Request Service'),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back_ios',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Progress indicator
            Container(
              padding: EdgeInsets.all(4.w),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Step ${_currentStep + 1} of $_totalSteps',
                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        '${((_currentStep + 1) / _totalSteps * 100).round()}%',
                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          color: AppTheme.lightTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 1.h),
                  LinearProgressIndicator(
                    value: (_currentStep + 1) / _totalSteps,
                    backgroundColor: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.lightTheme.primaryColor,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Form(
                key: _formKey,
                child: PageView(
                  controller: PageController(initialPage: _currentStep),
                  onPageChanged: (index) {
                    setState(() {
                      _currentStep = index;
                    });
                  },
                  children: [
                    // Step 1: Service Type Selection
                    _buildServiceTypeStep(),
                    
                    // Step 2: Location Selection
                    _buildLocationStep(),
                    
                    // Step 3: Vehicle Information
                    _buildVehicleInfoStep(),
                    
                    // Step 4: Review and Submit
                    _buildReviewStep(),
                  ],
                ),
              ),
            ),

            // Bottom navigation
            Container(
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.cardColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  if (_currentStep > 0)
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _previousStep,
                        child: const Text('Back'),
                      ),
                    ),
                  if (_currentStep > 0) SizedBox(width: 4.w),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _isLoading
                          ? null
                          : _currentStep == _totalSteps - 1
                              ? _submitRequest
                              : _canProceedToNextStep()
                                  ? _nextStep
                                  : null,
                      child: _isLoading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              _currentStep == _totalSteps - 1 ? 'Submit Request' : 'Next',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceTypeStep() {
    return Padding(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'What service do you need?',
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Select the type of roadside assistance you require',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 3.h),
          Expanded(
            child: ServiceTypeSelectorWidget(
              selectedServiceType: _selectedServiceType,
              onServiceTypeChanged: (serviceType) {
                setState(() {
                  _selectedServiceType = serviceType;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationStep() {
    return Padding(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Where do you need help?',
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Set your pickup location and destination if needed',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 3.h),
          Expanded(
            child: LocationPickerWidget(
              pickupLocation: _pickupLocation,
              dropoffLocation: _dropoffLocation,
              needsDropoff: _needsDropoff,
              onPickupLocationChanged: (location) {
                setState(() {
                  _pickupLocation = location;
                });
              },
              onDropoffLocationChanged: (location) {
                setState(() {
                  _dropoffLocation = location;
                });
              },
              onNeedsDropoffChanged: (needs) {
                setState(() {
                  _needsDropoff = needs;
                  if (!needs) {
                    _dropoffLocation = null;
                  }
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVehicleInfoStep() {
    return Padding(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Vehicle Information',
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Tell us about your vehicle so we can send the right help',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 3.h),
          Expanded(
            child: VehicleInfoWidget(
              makeController: _makeController,
              modelController: _modelController,
              yearController: _yearController,
              colorController: _colorController,
              licensePlateController: _licensePlateController,
              descriptionController: _descriptionController,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewStep() {
    return Padding(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Review Your Request',
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Please review your service request before submitting',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 3.h),
          
          // General Error Message
          if (_generalError != null) ...[
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.lightTheme.colorScheme.error.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'error_outline',
                    color: AppTheme.lightTheme.colorScheme.error,
                    size: 20,
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Text(
                      _generalError!,
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 2.h),
          ],

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Service Type
                  _buildReviewCard(
                    title: 'Service Type',
                    content: _selectedServiceType ?? 'Not selected',
                    icon: 'build',
                  ),
                  
                  SizedBox(height: 2.h),
                  
                  // Pickup Location
                  _buildReviewCard(
                    title: 'Pickup Location',
                    content: _pickupLocation?.address ?? 'Not selected',
                    icon: 'location_on',
                  ),
                  
                  if (_dropoffLocation != null) ...[
                    SizedBox(height: 2.h),
                    _buildReviewCard(
                      title: 'Dropoff Location',
                      content: _dropoffLocation!.address,
                      icon: 'flag',
                    ),
                  ],
                  
                  SizedBox(height: 2.h),
                  
                  // Vehicle Info
                  _buildReviewCard(
                    title: 'Vehicle',
                    content: '${_makeController.text} ${_modelController.text} ${_yearController.text}\n'
                             '${_colorController.text} • ${_licensePlateController.text}',
                    icon: 'directions_car',
                  ),
                  
                  if (_descriptionController.text.isNotEmpty) ...[
                    SizedBox(height: 2.h),
                    _buildReviewCard(
                      title: 'Additional Notes',
                      content: _descriptionController.text,
                      icon: 'note',
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewCard({
    required String title,
    required String content,
    required String icon,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(2.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: CustomIconWidget(
              iconName: icon,
              color: AppTheme.lightTheme.primaryColor,
              size: 20,
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  content,
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
