import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:sizer/sizer.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../../core/app_export.dart';
import 'destination_picker_screen.dart';

class RequestTowScreen extends StatefulWidget {
  final Position currentLocation;

  const RequestTowScreen({
    Key? key,
    required this.currentLocation,
  }) : super(key: key);

  @override
  State<RequestTowScreen> createState() => _RequestTowScreenState();
}

class _RequestTowScreenState extends State<RequestTowScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  
  // Controllers
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _vehicleInfoController = TextEditingController();
  final TextEditingController _locationDetailsController = TextEditingController();

  LatLng? _selectedDestination;

  // Focus nodes
  final FocusNode _descriptionFocusNode = FocusNode();
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _vehicleInfoFocusNode = FocusNode();
  final FocusNode _locationDetailsFocusNode = FocusNode();

  bool _isLoading = false;
  List<File> _images = [];
  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    _descriptionController.dispose();
    _phoneController.dispose();
    _vehicleInfoController.dispose();
    _locationDetailsController.dispose();
    
    _descriptionFocusNode.dispose();
    _phoneFocusNode.dispose();
    _vehicleInfoFocusNode.dispose();
    _locationDetailsFocusNode.dispose();
    super.dispose();
  }

  String? _validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return 'กรุณากรอก$fieldName';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'กรุณากรอกเบอร์โทรศัพท์';
    }
    if (!RegExp(r'^[0-9]{10}$').hasMatch(value.replaceAll('-', ''))) {
      return 'เบอร์โทรศัพท์ไม่ถูกต้อง (10 หลัก)';
    }
    return null;
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (images.isNotEmpty) {
        setState(() {
          _images.addAll(images.map((image) => File(image.path)));
          // Limit to 5 images
          if (_images.length > 5) {
            _images = _images.take(5).toList();
          }
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('เกิดข้อผิดพลาดในการเลือกรูปภาพ'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _removeImage(int index) {
    setState(() {
      _images.removeAt(index);
    });
  }

  Future<void> _selectDestination() async {
    final result = await Navigator.push<LatLng>(
      context,
      MaterialPageRoute(
        builder: (context) => DestinationPickerScreen(
          currentLocation: widget.currentLocation,
          selectedDestination: _selectedDestination,
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _selectedDestination = result;
      });
    }
  }

  Future<void> _requestTowTruck() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_images.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('กรุณาแนบรูปภาพอย่างน้อย 1 รูป'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 3));

      if (mounted) {
        // Show success dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 28,
                ),
                SizedBox(width: 2.w),
                Text(
                  'สำเร็จ!',
                  style: TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('คำขอเรียกรถลากของคุณได้รับการส่งแล้ว'),
                SizedBox(height: 2.h),
                Text(
                  'รถลากจะมาถึงภายใน 15-30 นาที',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.lightTheme.primaryColor,
                  ),
                ),
                SizedBox(height: 1.h),
                Text(
                  'คุณสามารถติดตามสถานะได้ในหน้าประวัติ',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  Navigator.of(context).pop(); // Go back to map
                  Navigator.of(context).pop(); // Go back to dashboard
                },
                child: Text(
                  'ตกลง',
                  style: TextStyle(
                    color: AppTheme.lightTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('เรียกรถลาก'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Location info
              Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.blue.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: Colors.blue,
                      size: 24,
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'ตำแหน่งปัจจุบัน',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.blue,
                            ),
                          ),
                          Text(
                            'Lat: ${widget.currentLocation.latitude.toStringAsFixed(6)}',
                            style: TextStyle(fontSize: 12.sp),
                          ),
                          Text(
                            'Lng: ${widget.currentLocation.longitude.toStringAsFixed(6)}',
                            style: TextStyle(fontSize: 12.sp),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 3.h),

              // Phone number
              Text(
                'เบอร์โทรศัพท์ *',
                style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 1.h),
              TextFormField(
                controller: _phoneController,
                focusNode: _phoneFocusNode,
                validator: _validatePhone,
                keyboardType: TextInputType.phone,
                decoration: InputDecoration(
                  hintText: '0812345678',
                  prefixIcon: Icon(Icons.phone),
                ),
                textInputAction: TextInputAction.next,
                onFieldSubmitted: (_) => _vehicleInfoFocusNode.requestFocus(),
              ),

              SizedBox(height: 2.h),

              // Vehicle info
              Text(
                'ข้อมูลรถ *',
                style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 1.h),
              TextFormField(
                controller: _vehicleInfoController,
                focusNode: _vehicleInfoFocusNode,
                validator: (value) => _validateRequired(value, 'ข้อมูลรถ'),
                decoration: InputDecoration(
                  hintText: 'เช่น Toyota Camry สีขาว ทะเบียน กข-1234',
                  prefixIcon: Icon(Icons.directions_car),
                ),
                textInputAction: TextInputAction.next,
                onFieldSubmitted: (_) => _locationDetailsFocusNode.requestFocus(),
              ),

              SizedBox(height: 2.h),

              // Location details
              Text(
                'รายละเอียดสถานที่ *',
                style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 1.h),
              TextFormField(
                controller: _locationDetailsController,
                focusNode: _locationDetailsFocusNode,
                validator: (value) => _validateRequired(value, 'รายละเอียดสถานที่'),
                maxLines: 2,
                decoration: InputDecoration(
                  hintText: 'เช่น หน้าร้าน 7-Eleven ถนนสุขุมวิท',
                  prefixIcon: Padding(
                    padding: EdgeInsets.only(bottom: 2.h),
                    child: Icon(Icons.location_on),
                  ),
                ),
                textInputAction: TextInputAction.next,
                onFieldSubmitted: (_) => _descriptionFocusNode.requestFocus(),
              ),

              SizedBox(height: 2.h),

              // Destination
              Text(
                'จุดหมาย (ไม่บังคับ)',
                style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 1.h),
              GestureDetector(
                onTap: _selectDestination,
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(4.w),
                  decoration: BoxDecoration(
                    color: AppTheme.lightTheme.cardColor,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _selectedDestination != null
                          ? Colors.green
                          : AppTheme.lightTheme.colorScheme.outline,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _selectedDestination != null ? Icons.location_on : Icons.add_location,
                        color: _selectedDestination != null ? Colors.green : Colors.grey,
                        size: 24,
                      ),
                      SizedBox(width: 3.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _selectedDestination != null
                                  ? 'จุดหมายที่เลือก'
                                  : 'เลือกจุดหมาย',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: _selectedDestination != null ? Colors.green : Colors.grey[600],
                              ),
                            ),
                            if (_selectedDestination != null) ...[
                              SizedBox(height: 0.5.h),
                              Text(
                                'Lat: ${_selectedDestination!.latitude.toStringAsFixed(6)}',
                                style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
                              ),
                              Text(
                                'Lng: ${_selectedDestination!.longitude.toStringAsFixed(6)}',
                                style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
                              ),
                            ] else ...[
                              SizedBox(height: 0.5.h),
                              Text(
                                'แตะเพื่อเลือกจุดหมายบนแผนที่',
                                style: TextStyle(fontSize: 12.sp, color: Colors.grey[500]),
                              ),
                            ],
                          ],
                        ),
                      ),
                      if (_selectedDestination != null)
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _selectedDestination = null;
                            });
                          },
                          icon: Icon(
                            Icons.clear,
                            color: Colors.red,
                            size: 20,
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 2.h),

              // Description
              Text(
                'รายละเอียดปัญหา *',
                style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 1.h),
              TextFormField(
                controller: _descriptionController,
                focusNode: _descriptionFocusNode,
                validator: (value) => _validateRequired(value, 'รายละเอียดปัญหา'),
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'อธิบายปัญหาที่เกิดขึ้น เช่น รถเสีย แบตหมด ยางแตก',
                  prefixIcon: Padding(
                    padding: EdgeInsets.only(bottom: 4.h),
                    child: Icon(Icons.description),
                  ),
                ),
                textInputAction: TextInputAction.done,
              ),

              SizedBox(height: 3.h),

              // Images section
              Text(
                'รูปภาพ * (สูงสุด 5 รูป)',
                style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 1.h),

              // Add image button
              GestureDetector(
                onTap: _images.length < 5 ? _pickImages : null,
                child: Container(
                  width: double.infinity,
                  height: 12.h,
                  decoration: BoxDecoration(
                    color: _images.length < 5 
                        ? AppTheme.lightTheme.cardColor 
                        : Colors.grey[300],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _images.length < 5 
                          ? AppTheme.lightTheme.primaryColor 
                          : Colors.grey,
                      style: BorderStyle.solid,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_photo_alternate,
                        size: 32,
                        color: _images.length < 5 
                            ? AppTheme.lightTheme.primaryColor 
                            : Colors.grey,
                      ),
                      SizedBox(height: 1.h),
                      Text(
                        _images.length < 5 
                            ? 'แนบรูปภาพ' 
                            : 'ครบ 5 รูปแล้ว',
                        style: TextStyle(
                          color: _images.length < 5 
                              ? AppTheme.lightTheme.primaryColor 
                              : Colors.grey,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Image preview
              if (_images.isNotEmpty) ...[
                SizedBox(height: 2.h),
                SizedBox(
                  height: 15.h,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _images.length,
                    itemBuilder: (context, index) {
                      return Container(
                        margin: EdgeInsets.only(right: 2.w),
                        child: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.file(
                                _images[index],
                                width: 20.w,
                                height: 15.h,
                                fit: BoxFit.cover,
                              ),
                            ),
                            Positioned(
                              top: 1.w,
                              right: 1.w,
                              child: GestureDetector(
                                onTap: () => _removeImage(index),
                                child: Container(
                                  padding: EdgeInsets.all(1.w),
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],

              SizedBox(height: 4.h),

              // Request button
              SizedBox(
                width: double.infinity,
                height: 7.h,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _requestTowTruck,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            SizedBox(width: 3.w),
                            Text(
                              'กำลังส่งคำขอ...',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.local_shipping,
                              size: 28,
                            ),
                            SizedBox(width: 3.w),
                            Text(
                              'เรียกรถลากเลย',
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                ),
              ),

              SizedBox(height: 2.h),
            ],
          ),
        ),
      ),
    );
  }
}
