// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:sizer/sizer.dart';

// import '../../core/app_export.dart';

// class RegisterScreen extends StatefulWidget {
//   const RegisterScreen({Key? key}) : super(key: key);

//   @override
//   State<RegisterScreen> createState() => _RegisterScreenState();
// }

// class _RegisterScreenState extends State<RegisterScreen>
//     with TickerProviderStateMixin {

//   String _selectedRole = AppConstants.userRoleCustomer;
//   bool _showForm = false;

//   final List<Map<String, dynamic>> _userRoles = [
//     {
//       'role': AppConstants.userRoleCustomer,
//       'title': 'Customer',
//       'subtitle': 'Request towing services',
//       'icon': 'person',
//       'color': Colors.blue,
//     },
//     {
//       'role': AppConstants.userRoleSupplier,
//       'title': 'Supplier',
//       'subtitle': 'Provide towing services',
//       'icon': 'local_shipping',
//       'color': Colors.green,
//     },
//     {
//       'role': AppConstants.userRoleAgent,
//       'title': 'Agent',
//       'subtitle': 'Connect customers with suppliers',
//       'icon': 'business_center',
//       'color': Colors.orange,
//     },
//   ];

//   late AnimationController _fadeController;
//   late Animation<double> _fadeAnimation;

//   @override
//   void initState() {
//     super.initState();
//     _initializeAnimations();
//   }

//   void _initializeAnimations() {
//     _fadeController = AnimationController(
//       duration: const Duration(milliseconds: 800),
//       vsync: this,
//     );

//     _fadeAnimation = Tween<double>(
//       begin: 0.0,
//       end: 1.0,
//     ).animate(CurvedAnimation(
//       parent: _fadeController,
//       curve: Curves.easeInOut,
//     ));

//     _fadeController.forward();
//   }

//   void _handleRoleSelection(String role) {
//     setState(() {
//       _selectedRole = role;
//       _showForm = true;
//     });
//   }

//   void _handleBackToRoleSelection() {
//     setState(() {
//       _showForm = false;
//     });
//   }

//   void _navigateToLogin() {
//     Navigator.pushReplacementNamed(context, AppRoutes.loginScreen);
//   }

//   @override
//   void dispose() {
//     _fadeController.dispose();
//     super.dispose();
//   }

//   late AnimationController _fadeController;
//   late AnimationController _slideController;
//   late Animation<double> _fadeAnimation;
//   late Animation<Offset> _slideAnimation;

//   @override
//   void initState() {
//     super.initState();
//     _initializeAnimations();
//     _setupFocusListeners();
//   }

//   void _initializeAnimations() {
//     _fadeController = AnimationController(
//       duration: const Duration(milliseconds: 800),
//       vsync: this,
//     );
//     _slideController = AnimationController(
//       duration: const Duration(milliseconds: 600),
//       vsync: this,
//     );

//     _fadeAnimation = Tween<double>(
//       begin: 0.0,
//       end: 1.0,
//     ).animate(CurvedAnimation(
//       parent: _fadeController,
//       curve: Curves.easeInOut,
//     ));

//     _slideAnimation = Tween<Offset>(
//       begin: const Offset(0, 0.3),
//       end: Offset.zero,
//     ).animate(CurvedAnimation(
//       parent: _slideController,
//       curve: Curves.easeOutCubic,
//     ));

//     _fadeController.forward();
//     _slideController.forward();
//   }

//   void _setupFocusListeners() {
//     _firstNameFocusNode.addListener(() {
//       if (_firstNameFocusNode.hasFocus) _clearErrors();
//     });
//     _lastNameFocusNode.addListener(() {
//       if (_lastNameFocusNode.hasFocus) _clearErrors();
//     });
//     _emailFocusNode.addListener(() {
//       if (_emailFocusNode.hasFocus) _clearErrors();
//     });
//     _phoneFocusNode.addListener(() {
//       if (_phoneFocusNode.hasFocus) _clearErrors();
//     });
//     _passwordFocusNode.addListener(() {
//       if (_passwordFocusNode.hasFocus) _clearErrors();
//     });
//     _confirmPasswordFocusNode.addListener(() {
//       if (_confirmPasswordFocusNode.hasFocus) _clearErrors();
//     });
//   }

//   void _clearErrors() {
//     if (_firstNameError != null ||
//         _lastNameError != null ||
//         _emailError != null ||
//         _phoneError != null ||
//         _passwordError != null ||
//         _confirmPasswordError != null ||
//         _generalError != null) {
//       setState(() {
//         _firstNameError = null;
//         _lastNameError = null;
//         _emailError = null;
//         _phoneError = null;
//         _passwordError = null;
//         _confirmPasswordError = null;
//         _generalError = null;
//       });
//     }
//   }

//   bool _validateInputs() {
//     bool isValid = true;

//     // First name validation
//     if (_firstNameController.text.trim().isEmpty) {
//       setState(() {
//         _firstNameError = 'First name is required';
//       });
//       isValid = false;
//     }

//     // Last name validation
//     if (_lastNameController.text.trim().isEmpty) {
//       setState(() {
//         _lastNameError = 'Last name is required';
//       });
//       isValid = false;
//     }

//     // Email validation
//     if (_emailController.text.trim().isEmpty) {
//       setState(() {
//         _emailError = 'Email is required';
//       });
//       isValid = false;
//     } else if (!RegExp(AppConstants.emailPattern).hasMatch(_emailController.text.trim())) {
//       setState(() {
//         _emailError = 'Please enter a valid email address';
//       });
//       isValid = false;
//     }

//     // Phone validation
//     if (_phoneController.text.trim().isEmpty) {
//       setState(() {
//         _phoneError = 'Phone number is required';
//       });
//       isValid = false;
//     } else if (!RegExp(AppConstants.phonePattern).hasMatch(_phoneController.text.trim())) {
//       setState(() {
//         _phoneError = 'Please enter a valid phone number';
//       });
//       isValid = false;
//     }

//     // Password validation
//     if (_passwordController.text.isEmpty) {
//       setState(() {
//         _passwordError = 'Password is required';
//       });
//       isValid = false;
//     } else if (_passwordController.text.length < AppConstants.minPasswordLength) {
//       setState(() {
//         _passwordError = 'Password must be at least ${AppConstants.minPasswordLength} characters';
//       });
//       isValid = false;
//     }

//     // Confirm password validation
//     if (_confirmPasswordController.text.isEmpty) {
//       setState(() {
//         _confirmPasswordError = 'Please confirm your password';
//       });
//       isValid = false;
//     } else if (_passwordController.text != _confirmPasswordController.text) {
//       setState(() {
//         _confirmPasswordError = 'Passwords do not match';
//       });
//       isValid = false;
//     }

//     // Terms agreement validation
//     if (!_agreeToTerms) {
//       setState(() {
//         _generalError = 'Please agree to the Terms and Conditions';
//       });
//       isValid = false;
//     }

//     return isValid;
//   }

//   Future<void> _handleRegister() async {
//     if (!_validateInputs()) return;

//     setState(() {
//       _isLoading = true;
//       _generalError = null;
//     });

//     try {
//       // Simulate registration API call
//       await Future.delayed(const Duration(seconds: 2));

//       // Success - trigger haptic feedback
//       HapticFeedback.lightImpact();

//       if (mounted) {
//         // Navigate to OTP verification screen
//         Navigator.pushNamed(
//           context,
//           '/otp-verification-screen',
//           arguments: {
//             'email': _emailController.text.trim(),
//             'phone': _phoneController.text.trim(),
//             'type': 'registration',
//           },
//         );
//       }
//     } catch (e) {
//       setState(() {
//         _generalError = 'Registration failed. Please try again.';
//         _isLoading = false;
//       });
//       HapticFeedback.heavyImpact();
//     }
//   }

//   void _handleBackNavigation() {
//     Navigator.pop(context);
//   }

//   void _handleLoginNavigation() {
//     Navigator.pushReplacementNamed(context, '/login-screen');
//   }

//   @override
//   void dispose() {
//     _fadeController.dispose();
//     _slideController.dispose();
//     _firstNameController.dispose();
//     _lastNameController.dispose();
//     _emailController.dispose();
//     _phoneController.dispose();
//     _passwordController.dispose();
//     _confirmPasswordController.dispose();
//     _firstNameFocusNode.dispose();
//     _lastNameFocusNode.dispose();
//     _emailFocusNode.dispose();
//     _phoneFocusNode.dispose();
//     _passwordFocusNode.dispose();
//     _confirmPasswordFocusNode.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
//       body: SafeArea(
//         child: GestureDetector(
//           onTap: () => FocusScope.of(context).unfocus(),
//           child: SingleChildScrollView(
//             physics: const ClampingScrollPhysics(),
//             child: ConstrainedBox(
//               constraints: BoxConstraints(
//                 minHeight: MediaQuery.of(context).size.height -
//                     MediaQuery.of(context).padding.top -
//                     MediaQuery.of(context).padding.bottom,
//               ),
//               child: IntrinsicHeight(
//                 child: Padding(
//                   padding: EdgeInsets.symmetric(horizontal: 6.w),
//                   child: FadeTransition(
//                     opacity: _fadeAnimation,
//                     child: SlideTransition(
//                       position: _slideAnimation,
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.center,
//                         children: [
//                           SizedBox(height: 4.h),

//                           // Back button
//                           Align(
//                             alignment: Alignment.centerLeft,
//                             child: IconButton(
//                               onPressed: _handleBackNavigation,
//                               icon: CustomIconWidget(
//                                 iconName: 'arrow_back_ios',
//                                 color: AppTheme.lightTheme.colorScheme.onSurface,
//                                 size: 24,
//                               ),
//                               padding: EdgeInsets.zero,
//                               constraints: const BoxConstraints(),
//                             ),
//                           ),

//                           SizedBox(height: 2.h),

//                           // App Logo
//                           Container(
//                             width: 20.w,
//                             height: 20.w,
//                             decoration: BoxDecoration(
//                               color: AppTheme.lightTheme.primaryColor,
//                               borderRadius: BorderRadius.circular(16),
//                               boxShadow: [
//                                 BoxShadow(
//                                   color: AppTheme.lightTheme.primaryColor
//                                       .withValues(alpha: 0.3),
//                                   blurRadius: 12,
//                                   offset: const Offset(0, 4),
//                                 ),
//                               ],
//                             ),
//                             child: Center(
//                               child: CustomIconWidget(
//                                 iconName: 'local_shipping',
//                                 color: Colors.white,
//                                 size: 32,
//                               ),
//                             ),
//                           ),

//                           SizedBox(height: 3.h),

//                           // Welcome text
//                           Text(
//                             'Create Account',
//                             style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
//                               fontWeight: FontWeight.w700,
//                             ),
//                             textAlign: TextAlign.center,
//                           ),

//                           SizedBox(height: 1.h),

//                           Text(
//                             'Join TowTruck Pro today',
//                             style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
//                               color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
//                             ),
//                             textAlign: TextAlign.center,
//                           ),

//                           SizedBox(height: 4.h),

//                           // Registration Form
//                           Form(
//                             key: _formKey,
//                             child: RegisterFormWidget(
//                               firstNameController: _firstNameController,
//                               lastNameController: _lastNameController,
//                               emailController: _emailController,
//                               phoneController: _phoneController,
//                               passwordController: _passwordController,
//                               confirmPasswordController: _confirmPasswordController,
//                               firstNameFocusNode: _firstNameFocusNode,
//                               lastNameFocusNode: _lastNameFocusNode,
//                               emailFocusNode: _emailFocusNode,
//                               phoneFocusNode: _phoneFocusNode,
//                               passwordFocusNode: _passwordFocusNode,
//                               confirmPasswordFocusNode: _confirmPasswordFocusNode,
//                               selectedRole: _selectedRole,
//                               isPasswordVisible: _isPasswordVisible,
//                               isConfirmPasswordVisible: _isConfirmPasswordVisible,
//                               agreeToTerms: _agreeToTerms,
//                               firstNameError: _firstNameError,
//                               lastNameError: _lastNameError,
//                               emailError: _emailError,
//                               phoneError: _phoneError,
//                               passwordError: _passwordError,
//                               confirmPasswordError: _confirmPasswordError,
//                               onRoleChanged: (role) {
//                                 setState(() {
//                                   _selectedRole = role;
//                                   _clearErrors();
//                                 });
//                               },
//                               onPasswordVisibilityToggle: () {
//                                 setState(() {
//                                   _isPasswordVisible = !_isPasswordVisible;
//                                 });
//                               },
//                               onConfirmPasswordVisibilityToggle: () {
//                                 setState(() {
//                                   _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
//                                 });
//                               },
//                               onTermsChanged: (value) {
//                                 setState(() {
//                                   _agreeToTerms = value;
//                                   _clearErrors();
//                                 });
//                               },
//                               onFieldSubmitted: () {
//                                 if (!_isLoading) {
//                                   _handleRegister();
//                                 }
//                               },
//                             ),
//                           ),

//                           SizedBox(height: 3.h),

//                           // General Error Message
//                           if (_generalError != null) ...[
//                             Container(
//                               width: double.infinity,
//                               padding: EdgeInsets.all(3.w),
//                               decoration: BoxDecoration(
//                                 color: AppTheme.lightTheme.colorScheme.error
//                                     .withValues(alpha: 0.1),
//                                 borderRadius: BorderRadius.circular(12),
//                                 border: Border.all(
//                                   color: AppTheme.lightTheme.colorScheme.error
//                                       .withValues(alpha: 0.3),
//                                 ),
//                               ),
//                               child: Row(
//                                 children: [
//                                   CustomIconWidget(
//                                     iconName: 'error_outline',
//                                     color: AppTheme.lightTheme.colorScheme.error,
//                                     size: 20,
//                                   ),
//                                   SizedBox(width: 2.w),
//                                   Expanded(
//                                     child: Text(
//                                       _generalError!,
//                                       style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
//                                         color: AppTheme.lightTheme.colorScheme.error,
//                                       ),
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                             SizedBox(height: 2.h),
//                           ],

//                           // Register Button
//                           SizedBox(
//                             width: double.infinity,
//                             height: 6.h,
//                             child: ElevatedButton(
//                               onPressed: _isLoading ? null : _handleRegister,
//                               style: AppTheme.lightTheme.elevatedButtonTheme.style?.copyWith(
//                                 backgroundColor: WidgetStateProperty.resolveWith((states) {
//                                   if (states.contains(WidgetState.disabled)) {
//                                     return AppTheme.lightTheme.colorScheme.onSurfaceVariant
//                                         .withValues(alpha: 0.3);
//                                   }
//                                   return AppTheme.lightTheme.primaryColor;
//                                 }),
//                               ),
//                               child: _isLoading
//                                   ? SizedBox(
//                                       width: 24,
//                                       height: 24,
//                                       child: CircularProgressIndicator(
//                                         strokeWidth: 2,
//                                         valueColor: AlwaysStoppedAnimation<Color>(
//                                           Colors.white,
//                                         ),
//                                       ),
//                                     )
//                                   : Text(
//                                       'Create Account',
//                                       style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
//                                         color: Colors.white,
//                                         fontWeight: FontWeight.w600,
//                                       ),
//                                     ),
//                             ),
//                           ),

//                           const Spacer(),

//                           // Login Link
//                           Padding(
//                             padding: EdgeInsets.only(bottom: 2.h),
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 Text(
//                                   'Already have an account? ',
//                                   style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
//                                     color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
//                                   ),
//                                 ),
//                                 TextButton(
//                                   onPressed: _isLoading ? null : _handleLoginNavigation,
//                                   style: TextButton.styleFrom(
//                                     padding: EdgeInsets.zero,
//                                     minimumSize: Size.zero,
//                                     tapTargetSize: MaterialTapTargetSize.shrinkWrap,
//                                   ),
//                                   child: Text(
//                                     'Sign In',
//                                     style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
//                                       color: AppTheme.lightTheme.primaryColor,
//                                       fontWeight: FontWeight.w600,
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
